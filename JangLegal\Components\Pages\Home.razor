﻿@page "/"
@using Microsoft.AspNetCore.Authorization
@using Syncfusion.Blazor.Charts
@using Syncfusion.Blazor.Schedule
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@using JangLegal.Models
@rendermode InteractiveServer

@inject AppAuthService AuthService
@inject NavigationManager NavMgr
@inject CourtCaseService CaseService
@inject CourtDataService CourtService
@inject CaseCategoryService CategoryService
@inject CaseVerdictService VerdictService
@attribute [Authorize()]
<PageTitle>Home</PageTitle>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h2>Legal Case Management Dashboard</h2>
        <div class="date-display">@DateTime.Now.ToString("dddd, MMMM dd, yyyy")</div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
        <div class="card summary-card">
            <div class="card-icon">
                <span class="material-icons">gavel</span>
            </div>
            <div class="card-content">
                <div class="card-title">Total Cases</div>
                <div class="card-value">@_totalCases</div>
            </div>
        </div>
        <div class="card summary-card">
            <div class="card-icon pending">
                <span class="material-icons">pending_actions</span>
            </div>
            <div class="card-content">
                <div class="card-title">Pending Cases</div>
                <div class="card-value">@_pendingCases</div>
            </div>
        </div>
        <div class="card summary-card">
            <div class="card-icon decided">
                <span class="material-icons">done_all</span>
            </div>
            <div class="card-content">
                <div class="card-title">Decided Cases</div>
                <div class="card-value">@_decidedCases</div>
            </div>
        </div>
        <div class="card summary-card">
            <div class="card-icon upcoming">
                <span class="material-icons">event</span>
            </div>
            <div class="card-content">
                <div class="card-title">Upcoming Hearings</div>
                <div class="card-value">@_upcomingHearings</div>
            </div>
        </div>
    </div>

    <div class="dashboard-row">
        <!-- Upcoming Hearings Calendar -->
        <div class="card dashboard-card calendar-card">
            <div class="card-header">
                <h3><span class="material-icons card-header-icon">event_note</span> Upcoming Hearings</h3>
                <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases"))" CssClass="e-outline"><span class="material-icons view-icon">list</span> View All Cases</SfButton>
            </div>
            <div class="card-body">
                <SfSchedule TValue="AppointmentData" Height="300px" SelectedDate="@_currentDate">
                    <ScheduleEventSettings DataSource="@_dataSource"></ScheduleEventSettings>
                    <ScheduleViews>
                        <ScheduleView Option="View.Day"></ScheduleView>
                        <ScheduleView Option="View.Week" IsSelected="true"></ScheduleView>
                        <ScheduleView Option="View.Month"></ScheduleView>
                    </ScheduleViews>
                </SfSchedule>
            </div>
        </div>

        <!-- Case Status Distribution -->
        <div class="card dashboard-card chart-card">
            <div class="card-header">
                <h3><span class="material-icons card-header-icon">pie_chart</span> Case Status Distribution</h3>
            </div>
            <div class="card-body">
                <SfAccumulationChart>
                    <AccumulationChartLegendSettings Visible="true" Position="LegendPosition.Right"></AccumulationChartLegendSettings>
                    <AccumulationChartTooltipSettings Enable="true"></AccumulationChartTooltipSettings>
                    <AccumulationChartSeriesCollection>
                        <AccumulationChartSeries DataSource="@_caseStatusData" XName="Status" YName="Count"
                                               InnerRadius="40%" LegendShape="LegendShape.Circle">
                            <AccumulationDataLabelSettings Visible="true" Name="Status" Position="AccumulationLabelPosition.Outside"></AccumulationDataLabelSettings>
                        </AccumulationChartSeries>
                    </AccumulationChartSeriesCollection>
                </SfAccumulationChart>
            </div>
        </div>
    </div>

    <div class="dashboard-row">
        <!-- Recent Cases -->
        <div class="card dashboard-card">
            <div class="card-header">
                <h3><span class="material-icons card-header-icon">description</span> Recent Cases</h3>
                <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases"))" CssClass="e-outline"><span class="material-icons view-icon">list</span> View All</SfButton>
            </div>
            <div class="card-body">
                <SfGrid DataSource="@_recentCases" AllowPaging="true" Height="300px">
                    <GridColumns>
                        <GridColumn Field="CaseNumber" HeaderText="Case #" Width="150px"></GridColumn>
                        <GridColumn Field="CourtName" HeaderText="Court" Width="200px"></GridColumn>
                        <GridColumn Field="Title" HeaderText="Title" Width="250px"></GridColumn>
                        <GridColumn Field="IsDecided" HeaderText="Status" Width="100px">
                            <Template>
                                @{
                                    if (context is CourtCaseDto caseItem)
                                    {
                                        if (caseItem.IsDecided)
                                        {
                                            <span class="status-badge decided"><span class="material-icons status-icon">done_all</span> Decided</span>
                                        }
                                        else
                                        {
                                            <span class="status-badge pending"><span class="material-icons status-icon">pending_actions</span> Pending</span>
                                        }
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="Actions" Width="100px">
                            <Template>
                                @{
                                    if (context is CourtCaseDto caseItem)
                                    {
                                        <SfButton OnClick="@(() => ViewCase(caseItem.Id))" CssClass="e-outline"><span class="material-icons view-icon">visibility</span> View</SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </div>
    </div>

    <div class="dashboard-row">
        <!-- Court Distribution -->
        <div class="card dashboard-card chart-card">
            <div class="card-header">
                <h3><span class="material-icons card-header-icon">account_balance</span> Cases by Court</h3>
            </div>
            <div class="card-body">
                <SfChart>
                    <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category"></ChartPrimaryXAxis>
                    <ChartSeriesCollection>
                        <ChartSeries DataSource="@_courtDistributionData" XName="Court" YName="Count" Type="ChartSeriesType.Column">
                        </ChartSeries>
                    </ChartSeriesCollection>
                </SfChart>
            </div>
        </div>

        <!-- Category Distribution -->
        <div class="card dashboard-card chart-card">
            <div class="card-header">
                <h3><span class="material-icons card-header-icon">category</span> Cases by Category</h3>
            </div>
            <div class="card-body">
                <SfAccumulationChart>
                    <AccumulationChartLegendSettings Visible="true" Position="LegendPosition.Right"></AccumulationChartLegendSettings>
                    <AccumulationChartTooltipSettings Enable="true"></AccumulationChartTooltipSettings>
                    <AccumulationChartSeriesCollection>
                        <AccumulationChartSeries DataSource="@_categoryDistributionData" XName="Category" YName="Count"
                                               InnerRadius="40%" LegendShape="LegendShape.Circle">
                            <AccumulationDataLabelSettings Visible="true" Name="Category" Position="AccumulationLabelPosition.Outside"></AccumulationDataLabelSettings>
                        </AccumulationChartSeries>
                    </AccumulationChartSeriesCollection>
                </SfAccumulationChart>
            </div>
        </div>
    </div>
</div>

@code {
    DateTime _currentDate = DateTime.Today;
    List<AppointmentData> _dataSource = new();
    List<CourtCaseDto> _courtCases = new();
    List<CourtCaseDto> _recentCases = new();
    List<ChartData> _caseStatusData = new();
    List<ChartData> _courtDistributionData = new();
    List<ChartData> _categoryDistributionData = new();

    int _totalCases = 0;
    int _pendingCases = 0;
    int _decidedCases = 0;
    int _upcomingHearings = 0;

    [CascadingParameter] private Task<AuthenticationState>? AuthenticationStateTask { get; set; }
    private string _userId = "";

    protected override async Task OnInitializedAsync()
    {
        var user = (await AuthenticationStateTask!).User;
        if (user is { Identity: { IsAuthenticated: true, Name: not null } })
            _userId = user.Identity.Name;

        @* var canAccessSite = await AuthService.CanUserAccessSite(_userId);
        if (!canAccessSite)
            NavMgr.NavigateTo("/not-authorized"); *@

        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        // Load court cases
        _courtCases = await CaseService.GetCourtCasesAsync();
        _totalCases = _courtCases.Count;
        _pendingCases = _courtCases.Count(c => !c.IsDecided);
        _decidedCases = _courtCases.Count(c => c.IsDecided);

        // Get recent cases (last 10)
        _recentCases = _courtCases
            .OrderByDescending(c => c.Id)
            .Take(10)
            .ToList();

        // For now, create sample appointment data
        // In a real implementation, you would use a service like:
        // _dataSource = await LegalService.GetCaseSchedule();
        _dataSource = GenerateSampleAppointments();
        _upcomingHearings = _dataSource.Count;

        // Prepare case status distribution data
        _caseStatusData = new List<ChartData>
        {
            new ChartData { Status = "Pending", Count = _pendingCases },
            new ChartData { Status = "Decided", Count = _decidedCases }
        };

        // Prepare court distribution data
        var courtGroups = _courtCases
            .GroupBy(c => c.CourtName)
            .Select(g => new ChartData { Court = g.Key ?? "Unknown", Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(5)
            .ToList();

        _courtDistributionData = courtGroups;

        // Prepare category distribution data
        var categoryGroups = _courtCases
            .GroupBy(c => c.CategoryTitle)
            .Select(g => new ChartData { Category = g.Key ?? "Uncategorized", Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(5)
            .ToList();

        _categoryDistributionData = categoryGroups;
    }

    private void ViewCase(int id)
    {
        NavMgr.NavigateTo($"/court-cases/edit/{id}");
    }

    private List<AppointmentData> GenerateSampleAppointments()
    {
        var appointments = new List<AppointmentData>();
        var random = new Random();

        // Generate some sample appointments for the next 14 days
        for (int i = 0; i < 5; i++)
        {
            var daysToAdd = random.Next(1, 14);
            var startTime = DateTime.Today.AddDays(daysToAdd).AddHours(9 + random.Next(0, 6));

            appointments.Add(new AppointmentData
            {
                Id = i + 1,
                Subject = $"Case Hearing #{i + 1}",
                Location = $"Court {random.Next(1, 5)}",
                StartTime = startTime,
                EndTime = startTime.AddHours(2),
                Description = $"Hearing for case #{random.Next(1000, 9999)}",
                IsAllDay = false,
                StartTimezone = "Asia/Karachi",
                EndTimezone = "Asia/Karachi"
            });
        }

        return appointments;
    }

    public class ChartData
    {
        public string? Status { get; set; }
        public string? Court { get; set; }
        public string? Category { get; set; }
        public int Count { get; set; }
    }
}