-- Seed AttachmentTypes table with common legal document types
-- This script will insert attachment types if they don't already exist

-- Check if AttachmentTypes table exists and has data
IF NOT EXISTS (SELECT 1 FROM [leg].[AttachmentTypes])
BEGIN
    INSERT INTO [leg].[AttachmentTypes] ([Title])
    VALUES 
        ('Pleading'),
        ('Exhibit'),
        ('Order'),
        ('Identification'),
        ('Other');
    
    PRINT 'AttachmentTypes seeded successfully';
END
ELSE
BEGIN
    -- Insert only missing types
    IF NOT EXISTS (SELECT 1 FROM [leg].[AttachmentTypes] WHERE [Title] = 'Pleading')
        INSERT INTO [leg].[AttachmentTypes] ([Title]) VALUES ('Pleading');
    
    IF NOT EXISTS (SELECT 1 FROM [leg].[AttachmentTypes] WHERE [Title] = 'Exhibit')
        INSERT INTO [leg].[AttachmentTypes] ([Title]) VALUES ('Exhibit');
    
    IF NOT EXISTS (SELECT 1 FROM [leg].[AttachmentTypes] WHERE [Title] = 'Order')
        INSERT INTO [leg].[AttachmentTypes] ([Title]) VALUES ('Order');
    
    IF NOT EXISTS (SELECT 1 FROM [leg].[AttachmentTypes] WHERE [Title] = 'Identification')
        INSERT INTO [leg].[AttachmentTypes] ([Title]) VALUES ('Identification');
    
    IF NOT EXISTS (SELECT 1 FROM [leg].[AttachmentTypes] WHERE [Title] = 'Other')
        INSERT INTO [leg].[AttachmentTypes] ([Title]) VALUES ('Other');
    
    PRINT 'AttachmentTypes checked and missing types added';
END

-- Display current attachment types
SELECT * FROM [leg].[AttachmentTypes] ORDER BY [Title];
