﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace JangLegal.Models;

public partial class CourtCase
{
    public int Id { get; set; }

    public string CaseNumber { get; set; }

    public string Title { get; set; }

    public int CourtId { get; set; }

    public int? CaseFilingYear { get; set; }

    public int? CaseVerdictId { get; set; }

    public DateTime? DateInOffice { get; set; }

    public decimal? ClaimAmount { get; set; }

    public int? CaseCategoryId { get; set; }

    public int? StayOrderId { get; set; }

    public bool IsDeleted { get; set; }

    public string CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public string ModifiedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string BriefFacts { get; set; }

    public string Pray { get; set; }

    public string CaseSynopsis { get; set; }

    public int? CaseNatureId { get; set; }

    public bool IsDecided { get; set; }

    public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();

    public virtual CaseCategory CaseCategory { get; set; }

    public virtual CaseNature CaseNature { get; set; }

    public virtual ICollection<CaseProceeding> CaseProceedings { get; set; } = new List<CaseProceeding>();

    public virtual CaseVerdict CaseVerdict { get; set; }

    public virtual Court Court { get; set; }

    public virtual ICollection<Plaintiff> Plaintiffs { get; set; } = new List<Plaintiff>();

    public virtual ICollection<Respondent> Respondents { get; set; } = new List<Respondent>();

    public virtual StayOrder StayOrder { get; set; }
}