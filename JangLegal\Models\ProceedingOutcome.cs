﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace JangLegal.Models;

public partial class ProceedingOutcome
{
    public int OutcomeId { get; set; }

    public int ProceedingId { get; set; }

    public string OutcomeDescription { get; set; }

    public string OrderIssuedBy { get; set; }

    public DateOnly? OrderDate { get; set; }

    public string DocumentReference { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? OutcomeTypeId { get; set; }

    public virtual OutcomeType OutcomeType { get; set; }

    public virtual CaseProceeding Proceeding { get; set; }
}