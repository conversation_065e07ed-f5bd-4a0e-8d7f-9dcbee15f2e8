using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CouncilOfGroupCompanyService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<CouncilOfGroupCompanyDto>> GetCouncilsAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CouncilOfGroupCompanies
            .Where(c => !c.IsDeleted)
            .Select(c => new CouncilOfGroupCompanyDto
            {
                Id = c.Id,
                Title = c.Title
            })
            .OrderBy(c => c.Title)
            .ToListAsync();
    }

    public async Task<CouncilOfGroupCompanyDto?> GetCouncilByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CouncilOfGroupCompanies
            .Where(c => c.Id == id && !c.IsDeleted)
            .Select(c => new CouncilOfGroupCompanyDto
            {
                Id = c.Id,
                Title = c.Title
            })
            .FirstOrDefaultAsync();
    }

    public async Task<string> SaveCouncilAsync(CouncilOfGroupCompanyDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            if (dto.Id == 0)
            {
                if (await dc.CouncilOfGroupCompanies
                    .AnyAsync(c => c.Title.ToLower() == dto.Title.ToLower() && !c.IsDeleted))
                {
                    return "A council with this title already exists";
                }

                var council = new CouncilOfGroupCompany
                {
                    Title = dto.Title.Trim(),
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.CouncilOfGroupCompanies.Add(council);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                var council = await dc.CouncilOfGroupCompanies.FindAsync(dto.Id);
                if (council == null)
                    return "Council not found";

                if (await dc.CouncilOfGroupCompanies
                    .AnyAsync(c => c.Id != dto.Id && c.Title.ToLower() == dto.Title.ToLower() && !c.IsDeleted))
                {
                    return "A council with this title already exists";
                }

                council.Title = dto.Title.Trim();
                council.ModifiedBy = userId;
                council.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeleteCouncilAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            //var council = await dc.CouncilOfGroupCompanies.FindAsync(id);
            var council = (from a in dc.CouncilOfGroupCompanies
                where a.Id == id
                select a).FirstOrDefault();

            if (council == null)
                return "Council not found";

            if (council.Plaintiffs.Any())
                return "Cannot delete council as it is referenced by plaintiffs";

            council.IsDeleted = true;
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }
}