@page "/court-cases"
@rendermode InteractiveServer
@inject CourtCaseService CaseService
@inject NavigationManager NavMgr
@inject CourtDataService CourtService
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType

<div class="d-flex justify-content-between align-items-center mb-2">
    <span style="font-size:24px; font-weight:bold;">Court Cases</span>
    <div class="d-flex align-items-center">
        <div class="me-3">
            <div class="card shadow-sm border-primary" style="width: 140px;">
                <div class="card-body p-2">
                    <div class="d-flex align-items-center">
                        <i class="e-icons e-folder-briefcase text-primary me-2 fs-5"></i>
                        <div style="display: flex; gap : 10px;">
                            <div class="text-muted small"><b>Total Cases</b></div>
                            <div class="fw-bold">@_courtCases.DistinctBy(c => c.<PERSON>umber).Count()</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <SfButton OnClick="OpenNewCaseForm" CssClass="e-primary">Add New Case</SfButton>
    </div>
</div>

<style>
    .case-filter-section {
        margin-bottom: 0.5rem;
    }

    .case-filter-row {
        display: grid;
        grid-template-columns: 3fr 2fr 2fr 3fr 2fr 1fr;
        gap: 0.5rem;
    }

    .case-filter-item {
        min-width: 0;
    }

    .case-filter-button {
        align-self: end;
    }
</style>

<style media="(max-width: 992px)">
    .case-filter-row {
        grid-template-columns: 1fr 1fr;
    }
</style>

<style media="(max-width: 576px)">
    .case-filter-row {
        grid-template-columns: 1fr;
    }
</style>

<div class="case-filter-section">
    <div class="case-filter-row">
        <div class="case-filter-item">
            <SfMultiSelect TValue="string[]" TItem="CaseEmployeeDto"
                          AllowFiltering="true"
                          Placeholder="Select Employees"
                          DataSource="@_employees"
                          @bind-Value="_selectedEmployees"
                          Mode="VisualMode.CheckBox"
                          FilterType="FilterType.Contains"
                          PopupWidth="800px"
                          ShowSelectAll="true">
                <MultiSelectFieldSettings Text="Name" Value="Id"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>
        <div class="case-filter-item">
            <SfMultiSelect TValue="string[]" TItem="CourtDto"
                          Placeholder="Select Courts"
                          DataSource="@_courts"
                          @bind-Value="_selectedCourts"
                          Mode="VisualMode.CheckBox"
                          FilterType="FilterType.Contains"
                          PopupWidth="400px"
                          ShowSelectAll="true">
                <MultiSelectFieldSettings Text="Name" Value="Id"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>
        <div class="case-filter-item">
            <SfMultiSelect TValue="string[]" TItem="CaseCategoryDto"
                          Placeholder="Select Categories"
                          DataSource="@_categories"
                          @bind-Value="_selectedCategories"
                          Mode="VisualMode.CheckBox"
                          FilterType="FilterType.Contains"
                          PopupWidth="500px"
                          ShowSelectAll="true">
                <MultiSelectFieldSettings Text="Title" Value="Id"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>
        <div class="case-filter-item">
            <SfMultiSelect TValue="string[]" TItem="CaseNatureDto"
                          Placeholder="Select Case Natures"
                          DataSource="@_caseNatures"
                          @bind-Value="_selectedCaseNatures"
                          Mode="VisualMode.CheckBox"
                          PopupWidth="500px"
                          FilterType="FilterType.Contains"
                          ShowSelectAll="true">
                <MultiSelectFieldSettings Text="Name" Value="Id"></MultiSelectFieldSettings>
            </SfMultiSelect>
        </div>
        <div class="case-filter-item">
            <SfDropDownList TValue="int" TItem="KeyValuePair<int, string>"
                           Placeholder="Decided Status"
                           DataSource="@_decidedOptions"
                           @bind-Value="_decidedFilterValue">
                <DropDownListFieldSettings Text="Value" Value="Key"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
        <div class="case-filter-item case-filter-button">
            <SfButton OnClick="ApplyFilters" CssClass="e-primary w-100">Filter</SfButton>
        </div>
    </div>
</div>
<SfGrid DataSource="_courtCases" AllowSorting="true" AllowFiltering="true"
EnableVirtualization="true" AllowTextWrap="true"
        Width="100%" Height="calc(100vh - 210px)">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"

    ></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="@nameof(CourtCaseDto.CaseNumber)" HeaderText="Case #" Width="200px"></GridColumn>
        <GridColumn Field="@nameof(CourtCaseDto.CourtName)" HeaderText="Court" Width="300px"></GridColumn>
        <GridColumn Field="@nameof(CourtCaseDto.CaseFilingYear)" HeaderText="Filing Year" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(CourtCaseDto.PlaintiffCount)" HeaderText="Plaintiffs" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(CourtCaseDto.RespondentCount)" HeaderText="Respondents" AutoFit="true"></GridColumn>

        <GridColumn Field="@nameof(CourtCaseDto.CategoryTitle)" HeaderText="Category" AutoFit="true"></GridColumn>
        <GridColumn Field="@nameof(CourtCaseDto.IsDecidedStr)" HeaderText="Decided" AutoFit="true"></GridColumn>
        <GridColumn HeaderText="Actions" AutoFit="true">
            <Template>
                @{
                    if (context is CourtCaseDto caseDto)
                    {
                        <SfButton OnClick="@(() => ViewCase(caseDto.Id))" CssClass="e-primary">View</SfButton>
                    }
                }
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    private List<CourtCaseDto> _courtCases = new();
    private List<CaseEmployeeDto> _employees = new();
    private List<CourtDto> _courts = new();
    private List<LawFirmDto> _lawFirms = new();
    private List<CaseCategoryDto> _categories = new();
    private List<CaseNatureDto> _caseNatures = new();

    private string[] _selectedEmployees = Array.Empty<string>();
    private string[] _selectedCourts = Array.Empty<string>();
    private string[] _selectedLawFirms = Array.Empty<string>();
    private string[] _selectedCategories = Array.Empty<string>();
    private string[] _selectedCaseNatures = Array.Empty<string>();
    private int _decidedFilterValue = 0; // 0 = All, 1 = Yes, 2 = No

    private List<KeyValuePair<int, string>> _decidedOptions = new List<KeyValuePair<int, string>>
    {
        new KeyValuePair<int, string>(0, "All Cases"),
        new KeyValuePair<int, string>(1, "Decided"),
        new KeyValuePair<int, string>(2, "Non Decided")
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _courtCases = await CaseService.GetCourtCasesAsync();
        _employees = await CaseService.GetCaseEmployeesAsync();
        _courts = await CourtService.GetCourtsAsync();
        _categories = await CaseService.GetCategoriesAsync();
        _caseNatures = await CaseService.GetCaseNaturesAsync();
    }

    private async Task ApplyFilters()
    {
        // Convert dropdown value to bool?
        bool? isDecided = _decidedFilterValue switch
        {
            1 => true,   // Yes
            2 => false,  // No
            _ => null    // All (no filter)
        };

        _courtCases = await CaseService.GetCourtCasesAsync(
            _selectedEmployees,
            _selectedCourts,
            _selectedCategories,
            _selectedCaseNatures,
            isDecided
        );
    }

    private void OpenNewCaseForm()
    {
        NavMgr.NavigateTo("/court-cases/new");
    }

    private void ViewCase(int id)
    {
        NavMgr.NavigateTo($"/court-cases/edit/{id}");
    }
}













