using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class PlaintiffTypeService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<PlaintiffTypeDto>> GetPlaintiffTypesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.PlaintiffTypes
            .Select(p => new PlaintiffTypeDto
            {
                Id = p.Id,
                Title = p.Title
            })
            .OrderBy(p => p.Id)
            .ToListAsync();
    }
}
