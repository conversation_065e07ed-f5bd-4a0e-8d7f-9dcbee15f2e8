/* Dashboard Styles */
.dashboard-container {
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Add media query for mobile view */
@media (max-width: 600px) {
    .dashboard-container {
        padding: 0;
    }
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

@media (max-width: 600px) {
    .dashboard-header {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .dashboard-header h2 {
        font-size: 18px;
        width: 100%;
        text-align: center;
    }
    
    .date-display {
        width: 100%;
        text-align: center;
        font-size: 14px;
    }
}

.dashboard-header h2 {
    margin: 0;
    color: #333;
    font-weight: 600;
}

.date-display {
    font-size: 16px;
    color: #666;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: white;
    transition: transform 0.2s ease-in-out;
}

.summary-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #0078d4;
    color: white;
    margin-right: 15px;
}

.card-icon .material-icons {
    font-size: 24px;
}

.card-header-icon {
    font-size: 20px;
    vertical-align: middle;
    margin-right: 5px;
}

.card-icon.pending {
    background-color: #f7630c;
}

.card-icon.decided {
    background-color: #107c10;
}

.card-icon.upcoming {
    background-color: #5c2d91;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.card-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

/* Dashboard Cards */
.dashboard-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.dashboard-card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: white;
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 20px;
}

/* Calendar Card */
.calendar-card .card-body {
    padding: 10px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge .status-icon {
    font-size: 14px;
    margin-right: 4px;
}

.status-badge.pending {
    background-color: #fff4ce;
    color: #f7630c;
}

.status-badge.decided {
    background-color: #dff6dd;
    color: #107c10;
}

/* Button Icons */
.view-icon {
    font-size: 16px;
    vertical-align: middle;
    margin-right: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-row {
        grid-template-columns: 1fr;
    }

    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}



