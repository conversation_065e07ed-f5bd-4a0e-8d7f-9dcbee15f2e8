using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CaseVerdictService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<CaseVerdictDto>> GetVerdictsAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseVerdicts
            .Where(c => !c.IsDeleted)
            .Select(c => new CaseVerdictDto
            {
                Id = c.Id,
                Title = c.Title,
                Description = c.Description,
                
            })
            .ToListAsync();
    }

    public async Task<string> SaveVerdictAsync(CaseVerdictDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            if (dto.Id == 0)
            {
                var verdict = new CaseVerdict
                {
                    Title = dto.Title,
                    Description = dto.Description,
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.CaseVerdicts.Add(verdict);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                var verdict = await dc.CaseVerdicts.FindAsync(dto.Id);
                if (verdict == null)
                    return "Verdict not found";

                verdict.Title = dto.Title;
                verdict.Description = dto.Description;
                verdict.ModifiedBy = userId;
                verdict.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeleteVerdictAsync(int id, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            var verdict = await dc.CaseVerdicts
                .Include(v => v.CourtCases)
                .FirstOrDefaultAsync(v => v.Id == id);

            if (verdict == null) return "Verdict not found";
            if (verdict.CourtCases.Any()) return "Cannot delete verdict as it is associated with court cases";

            verdict.IsDeleted = true;
            verdict.ModifiedBy = userId;
            verdict.ModifiedDate = DateTime.Now;
            
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return $"Error: {ex.Message}";
        }
    }

    public async Task<CaseVerdictDto?> GetVerdictByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        var verdict = await dc.CaseVerdicts
            .Include(v => v.CourtCases)
            .FirstOrDefaultAsync(v => v.Id == id && !v.IsDeleted);

        if (verdict == null) return null;

        return new CaseVerdictDto
        {
            Id = verdict.Id,
            Title = verdict.Title,
            Description = verdict.Description,
            CaseCount = verdict.CourtCases.Count
        };
    }
}


