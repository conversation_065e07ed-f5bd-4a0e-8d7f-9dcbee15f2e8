using System.ComponentModel.DataAnnotations;

namespace JangLegal.DTO;

public class CaseDto
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Case number is required")]
    [StringLength(100, ErrorMessage = "Case number cannot be longer than 100 characters")]
    public string CaseNumber { get; set; }

    public string Title { get; set; }

    [Required(ErrorMessage = "Court is required")]
    public int CourtId { get; set; }
    public string CourtName { get; set; }

    public int? CaseFilingYear { get; set; }
    public int? PrayId { get; set; }
    public string PrayTitle { get; set; }
    public int? CaseSynopsisId { get; set; }
    public string CaseSynopsisTitle { get; set; }
    public int? CaseVerdictId { get; set; }
    public string CaseVerdictTitle { get; set; }
    public DateTime? DateInOffice { get; set; }
    public decimal? ClaimAmount { get; set; }

    [Required(ErrorMessage = "Case category is required")]
    public int? CaseCategoryId { get; set; }
    public string CategoryTitle { get; set; }

    public int? StayOrderId { get; set; }
    public string StayOrderTitle { get; set; }
    public bool IsDeleted { get; set; }
    
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? ModifiedDate { get; set; }
    public bool IsDecided { get; set; } 
    
    public string IsDecidedStr => IsDecided ? "Yes" : "No";

    [Required(ErrorMessage = "At least one plaintiff is required")]
    [MinLength(1, ErrorMessage = "At least one plaintiff is required")]
    public List<PlaintiffDto> Plaintiffs { get; set; } = new List<PlaintiffDto>();

    [Required(ErrorMessage = "At least one respondent is required")]
    [MinLength(1, ErrorMessage = "At least one respondent is required")]
    public List<RespondentDto> Respondents { get; set; } = new List<RespondentDto>();
}

