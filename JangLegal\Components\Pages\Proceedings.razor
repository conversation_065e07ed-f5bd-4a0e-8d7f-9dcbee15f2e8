@page "/proceedings/{CaseId:int}"
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@inject LegalDataService Service
@inject NavigationManager NavMgr
@rendermode InteractiveServer
@inject AppAuthService AuthService


<SfToast @ref="_toastObj"></SfToast>
<SfDialog AllowDragging="true" IsModal="true" ShowCloseIcon="true" Width="800px" Visible="IsFormVisible">
    <DialogTemplates>
        <Header>Proceeding Detail</Header>
        <Content>
            <EditForm FormName="proceeding_form" Model="SelectedProceeding" OnValidSubmit="SaveProceeding">
                <FluentValidationValidator @ref="_fluentValidationValidator" DisableAssemblyScanning="@true" />

                <ValidationSummary />
                <div class="row mb-2 mt-2">
                    <div class="col-md">
                        Case Proceeded: <SfSwitch @bind-Checked="SelectedProceeding.IsProceeded"></SfSwitch>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-2">
                        <SfDatePicker @bind-Value="SelectedProceeding.ProceedingDate" Placeholder="Proceeding Date"
                                      Format="d MMM, yyyy" FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                        <ValidationMessage For="@(() => SelectedProceeding.ProceedingDate)" />
                    </div>
                    <div class="col-md">
                        <SfTextBox @bind-Value="SelectedProceeding.LocationOfCourtHearing" Placeholder="Location"
                                   FloatLabelType="FloatLabelType.Always" />
                        <ValidationMessage For="@(() => SelectedProceeding.LocationOfCourtHearing)" />
                    </div>
                </div>
                @if (SelectedProceeding.IsProceeded)
                {
                    <div class="row mb-2">
                        <div class="col">

                            <SfTextBox Placeholder="Detail" Multiline="true"
                                       @bind-Value="SelectedProceeding.DetailOfCourtHearing"
                                       FloatLabelType="FloatLabelType.Always">
                            </SfTextBox>
                            <ValidationMessage For="@(() => SelectedProceeding.DetailOfCourtHearing)" />
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <SfTextBox Placeholder="Synopsis" Multiline="true"
                                       @bind-Value="SelectedProceeding.SynopsisOfProceedings"
                                       FloatLabelType="FloatLabelType.Always">
                            </SfTextBox>
                            <ValidationMessage For="@(() => SelectedProceeding.SynopsisOfProceedings)" />
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <SfTextBox Placeholder="Council Name" Multiline="true"
                                       @bind-Value="SelectedProceeding.NameOfCounselPresent"
                                       FloatLabelType="FloatLabelType.Always">
                            </SfTextBox>
                            <ValidationMessage For="@(() => SelectedProceeding.NameOfCounselPresent)" />
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col">
                            <SfTextBox Placeholder="Hearing Outcome" Multiline="true"
                                       @bind-Value="SelectedProceeding.HearingOutcome" FloatLabelType="FloatLabelType.Always">
                            </SfTextBox>
                            <ValidationMessage For="@(() => SelectedProceeding.HearingOutcome)" />
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-md">
                            Case Concluded <SfSwitch @bind-Checked="SelectedProceeding.IsCaseConcluded"></SfSwitch>
                        </div>
                    </div>
                    @if (SelectedProceeding.IsCaseConcluded)
                    {
                        <div class="row mb-2">
                            <div class="col-md">
                                <SfTextBox Multiline="true" @bind-Value="SelectedProceeding.Verdict" Placeholder="Verdict"
                                           FloatLabelType="FloatLabelType.Always">
                                </SfTextBox>
                                <ValidationMessage For="@(() => SelectedProceeding.Verdict)" />
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md">
                                <SfTextBox Multiline="true" @bind-Value="SelectedProceeding.NextCourseOfActionAfterVerdict"
                                           Placeholder="Next Course Of Action After Verdict" FloatLabelType="FloatLabelType.Always">
                                </SfTextBox>
                                <ValidationMessage For="@(() => SelectedProceeding.NextCourseOfActionAfterVerdict)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="row mb-2">
                            <div class="col-md-2">
                                <SfDatePicker @bind-Value="SelectedProceeding.NextHearingDate" Placeholder="Next Hearing Date"
                                              Format="d MMM, yyyy" FloatLabelType="FloatLabelType.Always">
                                </SfDatePicker>
                                <ValidationMessage For="@(() => SelectedProceeding.NextHearingDate)" />
                            </div>
                        </div>
                    }
                }

                <div class="row mb-2">
                    <div class="col">
                        <SfButton CssClass="e-primary" type="submit">Save</SfButton>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<h3>@CaseInfo.CaseTitle?.ToUpper() &gt;&gt; Proceedings</h3>
<div class="row mb-2">
    <div class="col-md-2">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.CourtCaseNumber" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Case Number">
        </SfTextBox>
    </div>
    <div class="col-md-4">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.CaseTitle" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Case Title">
        </SfTextBox>
    </div>
    <div class="col-md-2">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.EmployeeCode" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Employee Code">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.EmployeeName" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Employee">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.FatherName" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Father Name">
        </SfTextBox>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.Department" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Department">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.Designation" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Designation">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.Location" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Location">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.Company" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Company">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.CNIC" FloatLabelType="FloatLabelType.Always"
                   Placeholder="CNIC">
        </SfTextBox>
    </div>
</div>
<div class="row mb-2">
    <div class="col-md-3">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.CourtName" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Court Name">
        </SfTextBox>
    </div>
    <div class="col-md">
        <SfTextBox Readonly="true" @bind-Value="CaseInfo.Plaintiff" FloatLabelType="FloatLabelType.Always"
                   Placeholder="Plaintiff">
        </SfTextBox>
    </div>
    <div class="col-md">
        @*<SfTextBox Readonly="true" @bind-Value="CaseInfo.Respondents" FloatLabelType="FloatLabelType.Always"
            Placeholder="Respondents"></SfTextBox>*@
    </div>
</div>
<div class="row mb-2">
    <div class="col">
        <SfButton OnClick="OpenAddProceedingForm" CssClass="e-primary">Add Proceeding</SfButton>
    </div>
</div>
<div class="row mb-2">
    <div class="col">

        <SfGrid DataSource="CaseProceedings" AllowSorting="true" AllowFiltering="true" AllowTextWrap="true">
            <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
            <GridColumns>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.ProceedingDate)" Format="d MMM, yyyy"
                            HeaderText="Proceeding Date">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.LocationOfCourtHearing)" HeaderText="Location">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.DetailOfCourtHearing)" HeaderText="Detail">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.NameOfCounselPresent)"
                            HeaderText="Counsel Name">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.SynopsisOfProceedings)" HeaderText="Synopsis">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.HearingOutcome)" HeaderText="Outcome">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.NextHearingDate)" Format="d MMM, yyyy"
                            HeaderText="Next Hearing Date">
                </GridColumn>
                <GridColumn AutoFit="true" Field="@nameof(ProceedingDto.Status)"
                            HeaderText="Status">
                </GridColumn>

                <GridColumn HeaderText="Action" AutoFit="true">
                    <Template Context="cc">
                        @{
                            if (cc is ProceedingDto obj)
                            {
                                <SfButton OnClick="@(() => OpenEditForm(obj.Id))" CssClass="e-primary">Edit</SfButton>
                            }
                        }
                    </Template>
                </GridColumn>

            </GridColumns>
        </SfGrid>
    </div>
</div>

@code {
    [Parameter] public int CaseId { get; set; }
    public bool IsFormVisible { get; set; }
    private SfToast? _toastObj;
    public CaseDto CaseInfo { get; set; } = new();
    private List<ProceedingDto> CaseProceedings { get; set; } = new();

    private FluentValidationValidator? _fluentValidationValidator;
    [CascadingParameter] private Task<AuthenticationState>? AuthenticationStateTask { get; set; }
    private string _userId = "";

    protected override async Task OnParametersSetAsync()
    {
        var user = (await AuthenticationStateTask!).User;
        if (user is { Identity: { IsAuthenticated: true, Name: not null } })
            _userId = user.Identity.Name;

        var canAccessSite = await AuthService.CanUserAccessSite(_userId);
        if (!canAccessSite)
            NavMgr.NavigateTo("/not-authorized");

        await base.OnParametersSetAsync();
        CaseInfo = (await Service.GetCaseById(CaseId));
        CaseProceedings = await Service.GetProceedings(CaseId);
    }

    [SupplyParameterFromForm] public ProceedingDto SelectedProceeding { get; set; } = new();

    public async Task SaveProceeding()
    {
        var msg = await Service.SaveProceeding(SelectedProceeding, _userId);
        if (msg == "OK")
        {
            CaseProceedings = await Service.GetProceedings(CaseId);
            IsFormVisible = false;
        }
        else
        {
            var tm = new ToastModel
            {
                Content = msg,
                Title = "Error",
                ShowProgressBar = true,
                ShowCloseButton = true,
                Timeout = 5000
            };
            await _toastObj?.ShowAsync(tm)!;
        }
    }

    public void OpenAddProceedingForm()
    {
        SelectedProceeding = new ProceedingDto
        {
            CaseId = CaseId
        };
        IsFormVisible = true;
    }

    public async Task OpenEditForm(int proceedingId)
    {
        SelectedProceeding = (await Service.GetProceedingById(proceedingId))!;
        IsFormVisible = true;
    }

}