// File download functionality
window.downloadFile = (fileName, base64Data) => {
    const link = document.createElement('a');
    link.href = 'data:application/octet-stream;base64,' + base64Data;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// File size formatting
window.formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    let order = 0;
    let size = bytes;
    
    while (size >= 1024 && order < sizes.length - 1) {
        order++;
        size /= 1024;
    }
    
    return size.toFixed(2) + ' ' + sizes[order];
};
