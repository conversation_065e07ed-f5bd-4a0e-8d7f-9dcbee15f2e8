using System.ComponentModel.DataAnnotations;

namespace JangLegal.DTO;

public class ProceedingOutcomeDto
{
    public int OutcomeId { get; set; }
    
    [Required(ErrorMessage = "Proceeding ID is required")]
    public int ProceedingId { get; set; }
    
    [Required(ErrorMessage = "Outcome description is required")]
    [StringLength(2000, ErrorMessage = "Outcome description cannot be longer than 2000 characters")]
    public string OutcomeDescription { get; set; } = string.Empty;
    
    [StringLength(255, ErrorMessage = "Order issued by cannot be longer than 255 characters")]
    public string OrderIssuedBy { get; set; } = string.Empty;
    
    public DateOnly? OrderDate { get; set; }
    
    [StringLength(255, ErrorMessage = "Document reference cannot be longer than 255 characters")]
    public string DocumentReference { get; set; } = string.Empty;
    
    public int? OutcomeTypeId { get; set; }
    public string OutcomeTypeName { get; set; } = string.Empty;
    
    // Computed properties for display
    public string OrderDateFormatted => OrderDate?.ToString("dd MMM, yyyy") ?? "";
}
