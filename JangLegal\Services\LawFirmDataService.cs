﻿using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class LawFirmDataService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public Task<List<LawFirmDto>> GetLawFirmsAsync()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var lawFirms = dc.LawFirms
            .Select(lf => new LawFirmDto
            {
                Id = lf.Id,
                Title = lf.Title,
                Address = lf.Address ?? string.Empty,
                Phone = lf.Phone ?? string.Empty,
                City = lf.City
            })
            .OrderBy(lf => lf.Title)
            .ToList();

        return Task.FromResult(lawFirms);
    }

    public Task<LawFirmDto?> GetLawFirmByIdAsync(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var lawFirm = dc.LawFirms
            .Where(lf => lf.Id == id)
            .Select(lf => new LawFirmDto
            {
                Id = lf.Id,
                Title = lf.Title,
                City = lf.City,
                Address = lf.Address ?? string.Empty,
                Phone = lf.Phone ?? string.Empty,
                Lawyers = lf.Lawyers.Select(l => new LawyerDto
                {
                    Id = l.Id,
                    Name = l.Name,
                    PhoneNumber = l.PhoneNumber,
                    Address = l.Address ?? string.Empty,
                    LawFirmId = l.LawFirmId,
                    LawFirmName = lf.Title,
                    City = l.City ?? string.Empty
                }).ToList()
            })
            .FirstOrDefault();

        return Task.FromResult(lawFirm);
    }

    public Task<List<LawyerDto>> GetLawyersAsync()
    {
        using var dc = dbContextFactory.CreateDbContext();
        var lawyers = dc.Lawyers
            .Include(l => l.LawFirm)
            .Select(l => new LawyerDto
            {
                Id = l.Id,
                Name = l.Name,
                PhoneNumber = l.PhoneNumber,
                Address = l.Address ?? string.Empty,
                LawFirmId = l.LawFirmId,
                LawFirmName = l.LawFirm.Title,
                City = l.City ?? string.Empty
            })
            .OrderBy(l => l.Name)
            .ToList();

        return Task.FromResult(lawyers);
    }

    public Task<LawyerDto?> GetLawyerByIdAsync(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        var lawyer = dc.Lawyers
            .Include(l => l.LawFirm)
            .Where(l => l.Id == id)
            .Select(l => new LawyerDto
            {
                Id = l.Id,
                Name = l.Name,
                PhoneNumber = l.PhoneNumber,
                Address = l.Address ?? string.Empty,
                LawFirmId = l.LawFirmId,
                LawFirmName = l.LawFirm.Title,
                City = l.City ?? string.Empty
            })
            .FirstOrDefault();

        return Task.FromResult(lawyer);
    }

    public Task<string> SaveLawyerAsync(LawyerDto dto, string userId)
    {
        using var dc = dbContextFactory.CreateDbContext();
        try
        {
            // Check if law firm exists
            var lawFirmExists = dc.LawFirms.Any(lf => lf.Id == dto.LawFirmId);
            if (!lawFirmExists) return Task.FromResult("Law firm not found");

            if (dto.Id == 0)
            {
                // Create new lawyer
                var lawyer = new Lawyer
                {
                    Name = dto.Name,
                    PhoneNumber = dto.PhoneNumber,
                    Address = dto.Address,
                    LawFirmId = dto.LawFirmId,
                    City = dto.City,
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.Lawyers.Add(lawyer);
                dc.SaveChanges();
                return Task.FromResult("OK");
            }
            else
            {
                // Update existing lawyer
                var lawyer = dc.Lawyers.Find(dto.Id);
                if (lawyer == null) return Task.FromResult("Lawyer not found");

                lawyer.Name = dto.Name;
                lawyer.PhoneNumber = dto.PhoneNumber;
                lawyer.Address = dto.Address;
                lawyer.LawFirmId = dto.LawFirmId;
                lawyer.City = dto.City;
                lawyer.ModifiedBy = userId;
                lawyer.ModifiedDate = DateTime.Now;

                dc.SaveChanges();
                return Task.FromResult("OK");
            }
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> SaveLawFirmAsync(LawFirmDto dto, string userId)
    {
        using var dc = dbContextFactory.CreateDbContext();
        dto.City = (dto.City).Trim();
        dto.Address = (dto.Address).Trim();
        dto.Title = (dto.Title).Trim();
        dto.Phone = (dto.Phone).Trim();
        try
        {
            if (dto.Id == 0)
            {
                // Check if law firm with same title already exists
                if (dc.LawFirms.Any(lf => lf.Title == dto.Title))
                    return Task.FromResult("A law firm with this title already exists");

                // Create new law firm
                var lawFirm = new LawFirm
                {
                    Title = dto.Title,
                    Address = dto.Address,
                    Phone = dto.Phone,
                    City = dto.City,
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.LawFirms.Add(lawFirm);
                dc.SaveChanges();
                return Task.FromResult("OK");
            }
            else
            {
                // Update existing law firm
                var lawFirm = dc.LawFirms.Find(dto.Id);
                if (lawFirm == null) return Task.FromResult("Law firm not found");

                // Check if another law firm with same title already exists
                if (dc.LawFirms.Any(lf => lf.Title == dto.Title && lf.Id != dto.Id))
                    return Task.FromResult("Another law firm with this title already exists");

                lawFirm.Title = dto.Title;
                lawFirm.Address = dto.Address;
                lawFirm.City = dto.City;
                lawFirm.Phone = dto.Phone;
                lawFirm.ModifiedBy = userId;
                lawFirm.ModifiedDate = DateTime.Now;

                dc.SaveChanges();
                return Task.FromResult("OK");
            }
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> DeleteLawyerAsync(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        try
        {
            var lawyer = dc.Lawyers.Find(id);
            if (lawyer == null) return Task.FromResult("Lawyer not found");

            dc.Lawyers.Remove(lawyer);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }

    public Task<string> DeleteLawFirmAsync(int id)
    {
        using var dc = dbContextFactory.CreateDbContext();
        try
        {
            var lawFirm = dc.LawFirms
                .Include(lf => lf.Lawyers)
                .FirstOrDefault(lf => lf.Id == id);

            if (lawFirm == null) return Task.FromResult("Law firm not found");

            // Check if the law firm has associated lawyers
            if (lawFirm.Lawyers.Any())
                return Task.FromResult(
                    "Cannot delete law firm with associated lawyers. Please remove all lawyers first.");

            dc.LawFirms.Remove(lawFirm);
            dc.SaveChanges();
            return Task.FromResult("OK");
        }
        catch (Exception ex)
        {
            return Task.FromResult(ex.Message);
        }
    }
}