using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CaseEmployeeService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<CaseEmployeeDto>> GetCaseEmployeesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseEmployees
            .Select(ce => new CaseEmployeeDto
            {
                Id = ce.Id,
                EmployeeCode = ce.EmployeeCode,
                Name = ce.Name,
                CNIC = ce.Cnic,
                FatherSpouseName = ce.FatherSpouseName,
                Location = ce.Location,
                Department = ce.Department,
                NatureOfContractId = ce.NatureOfContractId,
                NatureOfContractName = ce.NatureOfContract.Name,
                EmployeeStatusId = ce.EmployeeStatusId,
                EmployeeStatusName = ce.EmployeeStatus.Name,
                Designation = ce.Designation
            })
            .OrderBy(ce => ce.Name)
            .ToListAsync();
    }

    public async Task<CaseEmployeeDto?> GetCaseEmployeeByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseEmployees
            .Where(ce => ce.Id == id)
            .Select(ce => new CaseEmployeeDto
            {
                Id = ce.Id,
                EmployeeCode = ce.EmployeeCode,
                Name = ce.Name,
                CNIC = ce.Cnic,
                FatherSpouseName = ce.FatherSpouseName,
                Location = ce.Location,
                Department = ce.Department,
                NatureOfContractId = ce.NatureOfContractId,
                NatureOfContractName = ce.NatureOfContract.Name,
                EmployeeStatusId = ce.EmployeeStatusId,
                EmployeeStatusName = ce.EmployeeStatus.Name,
                Designation = ce.Designation
            })
            .FirstOrDefaultAsync();
    }

    public async Task<string> SaveCaseEmployeeAsync(CaseEmployeeDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            if (dto.Id == 0)
            {
                var caseEmployee = new CaseEmployee
                {
                    EmployeeCode = dto.EmployeeCode.Trim(),
                    Name = dto.Name.Trim(),
                    Cnic = dto.CNIC.Trim(),
                    FatherSpouseName = (dto.FatherSpouseName).Trim(),
                    Location = (dto.Location).Trim(),
                    Department = (dto.Department).Trim(),
                    NatureOfContractId = dto.NatureOfContractId,
                    EmployeeStatusId = dto.EmployeeStatusId,
                    Designation = (dto.Designation).Trim()
                };

                dc.CaseEmployees.Add(caseEmployee);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                var caseEmployee = await dc.CaseEmployees.FindAsync(dto.Id);
                if (caseEmployee == null)
                {
                    return "Record not found";
                }

                caseEmployee.EmployeeCode = dto.EmployeeCode.Trim();
                caseEmployee.Name = dto.Name.Trim();
                caseEmployee.Cnic = dto.CNIC.Trim();
                caseEmployee.FatherSpouseName = (dto.FatherSpouseName).Trim();
                caseEmployee.Location = (dto.Location).Trim();
                caseEmployee.Department = (dto.Department).Trim();
                caseEmployee.NatureOfContractId = dto.NatureOfContractId;
                caseEmployee.EmployeeStatusId = dto.EmployeeStatusId;
                caseEmployee.Designation = (dto.Designation).Trim();

                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeleteCaseEmployeeAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            var caseEmployee = await dc.CaseEmployees.FindAsync(id);
            if (caseEmployee == null)
            {
                return "Record not found";
            }

            dc.CaseEmployees.Remove(caseEmployee);
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<List<NatureOfContractDto>> GetNatureOfContractsAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.NatureOfContracts
            .Select(n => new NatureOfContractDto
            {
                Id = n.Id,
                Name = n.Name
            })
            .OrderBy(n => n.Name)
            .ToListAsync();
    }

    public async Task<List<CaseEmployeeStatusDto>> GetCaseEmployeeStatusesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseEmployeeStatuses
            .Select(s => new CaseEmployeeStatusDto
            {
                Id = s.Id,
                Name = s.Name
            })
            .OrderBy(s => s.Name)
            .ToListAsync();
    }
}