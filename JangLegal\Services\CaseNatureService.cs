using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CaseNatureService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<CaseNatureDto>> GetCaseNaturesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseNatures
            .Select(c => new CaseNatureDto
            {
                Id = c.Id,
                Name = c.Name
            })
            .OrderBy(c => c.Name)
            .ToListAsync();
    }

    public async Task<CaseNatureDto?> GetCaseNatureByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseNatures
            .Where(c => c.Id == id)
            .Select(c => new CaseNatureDto
            {
                Id = c.Id,
                Name = c.Name
            })
            .FirstOrDefaultAsync();
    }

    public async Task<string> SaveCaseNatureAsync(CaseNatureDto dto, string userId)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();

            if (dto.Id == 0)
            {
                if (await dc.CaseNatures.AnyAsync(c => c.Name.ToLower() == dto.Name.ToLower()))
                {
                    return "A case nature with this name already exists";
                }

                var caseNature = new CaseNature
                {
                    Name = dto.Name.Trim(),
                    CreateBy = userId,
                    CreateDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.CaseNatures.Add(caseNature);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                var caseNature = await dc.CaseNatures.FindAsync(dto.Id);
                if (caseNature == null)
                {
                    return "Case nature not found";
                }

                if (await dc.CaseNatures.AnyAsync(c => c.Name.ToLower() == dto.Name.ToLower() && c.Id != dto.Id))
                {
                    return "Another case nature with this name already exists";
                }

                caseNature.Name = dto.Name.Trim();
                caseNature.ModifiedBy = userId;
                caseNature.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeleteCaseNatureAsync(int id)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();

            var caseNature = await dc.CaseNatures.FindAsync(id);
            if (caseNature == null)
            {
                return "Case nature not found";
            }

            // Check if case nature is referenced in any courts
            if (await dc.Courts.AnyAsync(c => c.CaseNatureId == id))
            {
                return "Cannot delete case nature as it is being used by one or more courts";
            }

            dc.CaseNatures.Remove(caseNature);
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }
}