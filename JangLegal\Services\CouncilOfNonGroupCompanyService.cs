using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CouncilOfNonGroupCompanyService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<CouncilOfNonGroupCompanyDto>> GetCouncilsAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CouncilOfNonGroupCompanies
            .Where(c => !c.IsDeleted)
            .Select(c => new CouncilOfNonGroupCompanyDto
            {
                Id = c.Id,
                Title = c.Title
            })
            .OrderBy(c => c.Title)
            .ToListAsync();
    }

    public async Task<CouncilOfNonGroupCompanyDto?> GetCouncilByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CouncilOfNonGroupCompanies
            .Where(c => c.Id == id && !c.<PERSON>)
            .Select(c => new CouncilOfNonGroupCompanyDto
            {
                Id = c.Id,
                Title = c.Title
            })
            .FirstOrDefaultAsync();
    }

    public async Task<string> SaveCouncilAsync(CouncilOfNonGroupCompanyDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            if (dto.Id == 0)
            {
                if (await dc.CouncilOfNonGroupCompanies
                    .AnyAsync(c => c.Title.ToLower() == dto.Title.ToLower() && !c.IsDeleted))
                {
                    return "A council with this title already exists";
                }

                var council = new CouncilOfNonGroupCompany
                {
                    Title = dto.Title.Trim(),
                    CreatedBy = userId,
                    CreatedDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.CouncilOfNonGroupCompanies.Add(council);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                var existingCouncil = await dc.CouncilOfNonGroupCompanies
                    .FirstOrDefaultAsync(c => c.Id == dto.Id && !c.IsDeleted);

                if (existingCouncil == null)
                    return "Council not found";

                if (await dc.CouncilOfNonGroupCompanies
                    .AnyAsync(c => c.Id != dto.Id && c.Title.ToLower() == dto.Title.ToLower() && !c.IsDeleted))
                {
                    return "A council with this title already exists";
                }

                existingCouncil.Title = dto.Title.Trim();
                existingCouncil.ModifiedBy = userId;
                existingCouncil.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return $"Error: {ex.Message}";
        }
    }

    public async Task<string> DeleteCouncilAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            var council = await dc.CouncilOfNonGroupCompanies
                .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);

            if (council == null)
                return "Council not found";

            council.IsDeleted = true;
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return $"Error: {ex.Message}";
        }
    }
}