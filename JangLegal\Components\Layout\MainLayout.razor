﻿@using Orientation = Microsoft.FluentUI.AspNetCore.Components.Orientation
@inherits LayoutComponentBase

<FluentLayout>
    <FluentHeader>
        <span>Jang Legal</span>
        <FluentSpacer></FluentSpacer>
        <AuthorizeView>
            <Authorized>
                <span class="user-welcome">Welcome! @context.User.Identity.Name.Replace("GEO\\","")</span>
            </Authorized>
        </AuthorizeView>
    </FluentHeader>
    <FluentStack Class="main" Orientation="Orientation.Horizontal" Width="100%">
        <NavMenu/>
        <FluentBodyContent Class="body-content">
            <div class="content">
                @Body
            </div>
        </FluentBodyContent>
    </FluentStack>
    @*<FluentFooter>
        <div class="link1">
            <a href="https://www.fluentui-blazor.net" target="_blank">Documentation and demos</a>
        </div>
        <div class="link2">
            <a href="https://learn.microsoft.com/en-us/aspnet/core/blazor" target="_blank">About Blazor</a>
        </div>
    </FluentFooter>*@
</FluentLayout>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>
