using System.ComponentModel.DataAnnotations;

namespace JangLegal.DTO;

public class AttachmentDto
{
    public Guid Id { get; set; }
    public int CourtCaseId { get; set; }
    
    
    public string FileName { get; set; } = string.Empty;
    
    
    
    [StringLength(1000, ErrorMessage = "File path cannot be longer than 1000 characters")]
    public string FilePhysicalPath { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "File URL cannot be longer than 1000 characters")]
    public string FileUrl { get; set; } = string.Empty;
    
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string ModifiedBy { get; set; } = string.Empty;
    public DateTime? ModifiedDate { get; set; }
    
    [Required(ErrorMessage = "Attachment type is required")]
    public int? AttachmentTypeId { get; set; }
    public string AttachmentTypeName { get; set; } = string.Empty;
    
    // Additional properties for display
    public long FileSizeBytes { get; set; }
    public string FileSizeDisplay => FormatFileSize(FileSizeBytes);
    
    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }
}
