﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Models;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Attachment> Attachments { get; set; }

    public virtual DbSet<AttachmentType> AttachmentTypes { get; set; }

    public virtual DbSet<BusinessUnit> BusinessUnits { get; set; }

    public virtual DbSet<CaseCategory> CaseCategories { get; set; }

    public virtual DbSet<CaseEmployee> CaseEmployees { get; set; }

    public virtual DbSet<CaseEmployeeStatus> CaseEmployeeStatuses { get; set; }

    public virtual DbSet<CaseNature> CaseNatures { get; set; }

    public virtual DbSet<CaseProceeding> CaseProceedings { get; set; }

    public virtual DbSet<CaseVerdict> CaseVerdicts { get; set; }

    public virtual DbSet<City> Cities { get; set; }

    public virtual DbSet<Company> Companies { get; set; }

    public virtual DbSet<CouncilOfGroupCompany> CouncilOfGroupCompanies { get; set; }

    public virtual DbSet<CouncilOfNonGroupCompany> CouncilOfNonGroupCompanies { get; set; }

    public virtual DbSet<Court> Courts { get; set; }

    public virtual DbSet<CourtCase> CourtCases { get; set; }

    public virtual DbSet<Department> Departments { get; set; }

    public virtual DbSet<Designation> Designations { get; set; }

    public virtual DbSet<Employee> Employees { get; set; }

    public virtual DbSet<EmploymentStatus> EmploymentStatuses { get; set; }

    public virtual DbSet<GroupCompany> GroupCompanies { get; set; }

    public virtual DbSet<Judge> Judges { get; set; }

    public virtual DbSet<LawFirm> LawFirms { get; set; }

    public virtual DbSet<Lawyer> Lawyers { get; set; }

    public virtual DbSet<MaritalStatus> MaritalStatuses { get; set; }

    public virtual DbSet<NatureOfContract> NatureOfContracts { get; set; }

    public virtual DbSet<NonGroupCompany> NonGroupCompanies { get; set; }

    public virtual DbSet<OutcomeType> OutcomeTypes { get; set; }

    public virtual DbSet<Plaintiff> Plaintiffs { get; set; }

    public virtual DbSet<PlaintiffType> PlaintiffTypes { get; set; }

    public virtual DbSet<ProceedingAttendee> ProceedingAttendees { get; set; }

    public virtual DbSet<ProceedingDocument> ProceedingDocuments { get; set; }

    public virtual DbSet<ProceedingOutcome> ProceedingOutcomes { get; set; }

    public virtual DbSet<ProceedingStatus> ProceedingStatuses { get; set; }

    public virtual DbSet<ProceedingType> ProceedingTypes { get; set; }

    public virtual DbSet<RawEmployee> RawEmployees { get; set; }

    public virtual DbSet<Respondent> Respondents { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleInProceeding> RoleInProceedings { get; set; }

    public virtual DbSet<StatyOrderType> StatyOrderTypes { get; set; }

    public virtual DbSet<StayOrder> StayOrders { get; set; }

    public virtual DbSet<TypeOfEmployment> TypeOfEmployments { get; set; }

    public virtual DbSet<User> Users { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.UseCollation("Latin1_General_CI_AS");

        modelBuilder.Entity<Attachment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Attachments_Id");

            entity.ToTable("Attachments", "leg");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FileName)
                .IsRequired()
                .HasMaxLength(1000);
            entity.Property(e => e.FilePhysicalPath).HasMaxLength(1000);
            entity.Property(e => e.FileType)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.FileUrl)
                .HasMaxLength(1000)
                .HasColumnName("FileURL");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.AttachmentType).WithMany(p => p.Attachments)
                .HasForeignKey(d => d.AttachmentTypeId)
                .HasConstraintName("FK__Attachmen__Attac__7755B73D");

            entity.HasOne(d => d.CourtCase).WithMany(p => p.Attachments)
                .HasForeignKey(d => d.CourtCaseId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Attachmen__Court__73852659");
        });

        modelBuilder.Entity<AttachmentType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Attachme__3214EC0750F1B760");

            entity.ToTable("AttachmentTypes", "leg");

            entity.HasIndex(e => e.Title, "UQ__Attachme__2CB664DC93BD4712").IsUnique();

            entity.Property(e => e.Title)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<BusinessUnit>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Business__3214EC07DD33A3C3");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<CaseCategory>(entity =>
        {
            entity.Property(e => e.CreatedBy).IsRequired();
            entity.Property(e => e.Title).IsRequired();
        });

        modelBuilder.Entity<CaseEmployee>(entity =>
        {
            entity.HasIndex(e => e.EmployeeStatusId, "IX_CaseEmployees_EmployeeStatusId");

            entity.HasIndex(e => e.NatureOfContractId, "IX_CaseEmployees_NatureOfContractId");

            entity.Property(e => e.Cnic)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CNIC");
            entity.Property(e => e.Department)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Designation)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.EmployeeCode)
                .HasMaxLength(15)
                .IsUnicode(false);
            entity.Property(e => e.FatherSpouseName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Location)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.HasOne(d => d.EmployeeStatus).WithMany(p => p.CaseEmployees).HasForeignKey(d => d.EmployeeStatusId);

            entity.HasOne(d => d.NatureOfContract).WithMany(p => p.CaseEmployees).HasForeignKey(d => d.NatureOfContractId);
        });

        modelBuilder.Entity<CaseEmployeeStatus>(entity =>
        {
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<CaseNature>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CaseNatu__3214EC0758634384");

            entity.ToTable("CaseNatures", "leg");

            entity.HasIndex(e => e.Name, "UQ__CaseNatu__737584F6F1CDE12D").IsUnique();

            entity.Property(e => e.CreateBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200);
        });

        modelBuilder.Entity<CaseProceeding>(entity =>
        {
            entity.HasKey(e => e.ProceedingId).HasName("PK__Proceedi__9710D4BB4FD62C36");

            entity.ToTable("CaseProceedings", "leg");

            entity.Property(e => e.ProceedingId).HasColumnName("ProceedingID");
            entity.Property(e => e.CourtCaseId).HasColumnName("CourtCaseID");
            entity.Property(e => e.CourtRoomNumber).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.PresidingJudge).HasMaxLength(255);
            entity.Property(e => e.ProceedingStatusId).HasColumnName("ProceedingStatusID");
            entity.Property(e => e.ProceedingTypeId).HasColumnName("ProceedingTypeID");

            entity.HasOne(d => d.CourtCase).WithMany(p => p.CaseProceedings)
                .HasForeignKey(d => d.CourtCaseId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Proceedings_CourtCases");

            entity.HasOne(d => d.ProceedingStatus).WithMany(p => p.CaseProceedings)
                .HasForeignKey(d => d.ProceedingStatusId)
                .HasConstraintName("FK_Proceedings_ProceedingStatus");

            entity.HasOne(d => d.ProceedingType).WithMany(p => p.CaseProceedings)
                .HasForeignKey(d => d.ProceedingTypeId)
                .HasConstraintName("FK_Proceedings_ProceedingTypes");
        });

        modelBuilder.Entity<CaseVerdict>(entity =>
        {
            entity.Property(e => e.CreatedBy).IsRequired();
            entity.Property(e => e.Description).IsRequired();
            entity.Property(e => e.ModifiedBy).IsRequired();
            entity.Property(e => e.Title).IsRequired();
        });

        modelBuilder.Entity<City>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Cities__3214EC0723AE9A6A");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Company>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Companie__3214EC07C35FE504");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<CouncilOfGroupCompany>(entity =>
        {
            entity.Property(e => e.CreatedBy).IsRequired();
            entity.Property(e => e.Title).IsRequired();
        });

        modelBuilder.Entity<CouncilOfNonGroupCompany>(entity =>
        {
            entity.Property(e => e.CreatedBy).IsRequired();
            entity.Property(e => e.Title).IsRequired();
        });

        modelBuilder.Entity<Court>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Court_Id");

            entity.ToTable("Courts", "leg");

            entity.HasIndex(e => e.Name, "UQ__Courts__737584F638AFD205").IsUnique();

            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreateBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200);

            entity.HasOne(d => d.CaseNature).WithMany(p => p.Courts)
                .HasForeignKey(d => d.CaseNatureId)
                .HasConstraintName("FK__Courts__CaseNatu__65370702");
        });

        modelBuilder.Entity<CourtCase>(entity =>
        {
            entity.ToTable("CourtCases", "leg");

            entity.HasIndex(e => e.CaseCategoryId, "IX_CourtCases_CaseCategoryId");

            entity.HasIndex(e => e.CaseVerdictId, "IX_CourtCases_CaseVerdictId");

            entity.HasIndex(e => e.CourtId, "IX_CourtCases_CourtId");

            entity.HasIndex(e => e.StayOrderId, "IX_CourtCases_StayOrderId");

            entity.Property(e => e.CaseNumber)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.ClaimAmount).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.CreatedBy)
                .IsRequired()
                .HasMaxLength(300);
            entity.Property(e => e.ModifiedBy)
                .IsRequired()
                .HasMaxLength(300);
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(500);

            entity.HasOne(d => d.CaseCategory).WithMany(p => p.CourtCases).HasForeignKey(d => d.CaseCategoryId);

            entity.HasOne(d => d.CaseNature).WithMany(p => p.CourtCases)
                .HasForeignKey(d => d.CaseNatureId)
                .HasConstraintName("FK__CourtCase__CaseN__662B2B3B");

            entity.HasOne(d => d.CaseVerdict).WithMany(p => p.CourtCases).HasForeignKey(d => d.CaseVerdictId);

            entity.HasOne(d => d.Court).WithMany(p => p.CourtCases)
                .HasForeignKey(d => d.CourtId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.StayOrder).WithMany(p => p.CourtCases).HasForeignKey(d => d.StayOrderId);
        });

        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Departme__3214EC071F9C2847");

            entity.HasIndex(e => new { e.BusinessUnitId, e.Name }, "IX_Departments").IsUnique();

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.HasOne(d => d.BusinessUnit).WithMany(p => p.Departments)
                .HasForeignKey(d => d.BusinessUnitId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });

        modelBuilder.Entity<Designation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Designat__3214EC074785D670");

            entity.HasIndex(e => new { e.DepartmentId, e.Name }, "IX_Designations").IsUnique();

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.Department).WithMany(p => p.Designations)
                .HasForeignKey(d => d.DepartmentId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });

        modelBuilder.Entity<Employee>(entity =>
        {
            entity.HasKey(e => e.Code).HasName("PK__Employee__A25C5AA6233BCDC4");

            entity.HasIndex(e => e.CityId, "IX_Employees_CityId");

            entity.HasIndex(e => e.CompanyId, "IX_Employees_CompanyId");

            entity.HasIndex(e => e.DesignationId, "IX_Employees_DesignationId");

            entity.HasIndex(e => e.MaritalStatusId, "IX_Employees_MaritalStatusId");

            entity.HasIndex(e => e.StatusId, "IX_Employees_StatusId");

            entity.HasIndex(e => e.TypeOfEmploymentId, "IX_Employees_TypeOfEmploymentId");

            entity.HasIndex(e => e.StationId, "idx_EmployeeStation");

            entity.Property(e => e.Code)
                .HasMaxLength(15)
                .IsUnicode(false);
            entity.Property(e => e.Cnic)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CNIC");
            entity.Property(e => e.EmailOfficial)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.FatherName)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.JobType).HasDefaultValue((byte)1);
            entity.Property(e => e.MobileNumber)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ReasonOfExit)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.StatusId).HasDefaultValue((byte)1);

            entity.HasOne(d => d.City).WithMany(p => p.EmployeeCities)
                .HasForeignKey(d => d.CityId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.Company).WithMany(p => p.Employees).HasForeignKey(d => d.CompanyId);

            entity.HasOne(d => d.Designation).WithMany(p => p.Employees)
                .HasForeignKey(d => d.DesignationId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.MaritalStatus).WithMany(p => p.Employees).HasForeignKey(d => d.MaritalStatusId);

            entity.HasOne(d => d.Station).WithMany(p => p.EmployeeStations)
                .HasForeignKey(d => d.StationId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.Status).WithMany(p => p.Employees)
                .HasForeignKey(d => d.StatusId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.TypeOfEmployment).WithMany(p => p.Employees).HasForeignKey(d => d.TypeOfEmploymentId);
        });

        modelBuilder.Entity<EmploymentStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Employme__3214EC07DD981916");

            entity.ToTable("EmploymentStatus");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<GroupCompany>(entity =>
        {
            entity.ToTable("GroupCompanies", "leg");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200);
        });

        modelBuilder.Entity<LawFirm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__LawFirms__3214EC0779910800");

            entity.ToTable("LawFirms", "leg");

            entity.HasIndex(e => e.Title, "UQ__LawFirms__2CB664DCA849896B").IsUnique();

            entity.Property(e => e.Address).HasMaxLength(1000);
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Phone)
                .HasMaxLength(30)
                .IsUnicode(false);
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(500);
        });

        modelBuilder.Entity<Lawyer>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Lawyers__3214EC07DB14C015");

            entity.ToTable("Lawyers", "leg");

            entity.HasIndex(e => e.LawFirmId, "IX_Lawyers_LawFirmId");

            entity.Property(e => e.Address)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.City)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200);
            entity.Property(e => e.PhoneNumber)
                .IsRequired()
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.HasOne(d => d.LawFirm).WithMany(p => p.Lawyers)
                .HasForeignKey(d => d.LawFirmId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });

        modelBuilder.Entity<MaritalStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MaritalS__3214EC070A572248");

            entity.ToTable("MaritalStatus");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<NatureOfContract>(entity =>
        {
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<NonGroupCompany>(entity =>
        {
            entity.ToTable("NonGroupCompanies", "leg");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(200);
        });

        modelBuilder.Entity<OutcomeType>(entity =>
        {
            entity.HasKey(e => e.OutcomeTypeId).HasName("PK__OutcomeT__9066082691AE51B1");

            entity.ToTable("OutcomeTypes", "leg");

            entity.HasIndex(e => e.TypeName, "UQ__OutcomeT__D4E7DFA881C0DC30").IsUnique();

            entity.Property(e => e.OutcomeTypeId).HasColumnName("OutcomeTypeID");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.TypeName)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<Plaintiff>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Plaintiffs_Id");

            entity.ToTable("Plaintiffs", "leg");

            entity.HasIndex(e => e.CaseEmployeeId, "IX_Plaintiffs_CaseEmployeeId");

            entity.HasIndex(e => e.CouncilOfGroupCompanyId, "IX_Plaintiffs_CouncilOfGroupCompanyId");

            entity.HasIndex(e => e.CouncilOfNonGroupCompanyId, "IX_Plaintiffs_CouncilOfNonGroupCompanyId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedBy)
                .IsRequired()
                .HasMaxLength(300)
                .HasDefaultValue("");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.ModifiedBy)
                .IsRequired()
                .HasMaxLength(300)
                .HasDefaultValue("");
            entity.Property(e => e.OtherPlaintiff)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.TypeId).HasDefaultValue(1);

            entity.HasOne(d => d.CaseEmployee).WithMany(p => p.Plaintiffs).HasForeignKey(d => d.CaseEmployeeId);

            entity.HasOne(d => d.CouncilOfGroupCompany).WithMany(p => p.Plaintiffs).HasForeignKey(d => d.CouncilOfGroupCompanyId);

            entity.HasOne(d => d.CouncilOfNonGroupCompany).WithMany(p => p.Plaintiffs).HasForeignKey(d => d.CouncilOfNonGroupCompanyId);

            entity.HasOne(d => d.CourtCase).WithMany(p => p.Plaintiffs)
                .HasForeignKey(d => d.CourtCaseId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.LawFirm).WithMany(p => p.Plaintiffs)
                .HasForeignKey(d => d.LawFirmId)
                .HasConstraintName("FK__Plaintiff__LawFi__6DCC4D03");

            entity.HasOne(d => d.LawFirmNavigation).WithMany(p => p.Plaintiffs)
                .HasForeignKey(d => d.LawFirmId)
                .HasConstraintName("FK__Plaintiff__LawFi__6CD828CA");

            entity.HasOne(d => d.Type).WithMany(p => p.Plaintiffs)
                .HasForeignKey(d => d.TypeId)
                .HasConstraintName("FK__Plaintiff__TypeI__6AEFE058");
        });

        modelBuilder.Entity<PlaintiffType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Plaintif__3214EC070CDA378F");

            entity.HasIndex(e => e.Title, "UQ__Plaintif__2CB664DC5B30EC9C").IsUnique();

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ProceedingAttendee>(entity =>
        {
            entity.HasKey(e => e.AttendeeId).HasName("PK__Proceedi__184401287B536049");

            entity.ToTable("ProceedingAttendees", "leg");

            entity.Property(e => e.AttendeeId).HasColumnName("AttendeeID");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsPresent).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.PersonName)
                .IsRequired()
                .HasMaxLength(255);
            entity.Property(e => e.ProceedingId).HasColumnName("ProceedingID");
            entity.Property(e => e.RoleInProceedingId).HasColumnName("RoleInProceedingID");

            entity.HasOne(d => d.Proceeding).WithMany(p => p.ProceedingAttendees)
                .HasForeignKey(d => d.ProceedingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProceedingAttendees_Proceedings");

            entity.HasOne(d => d.RoleInProceeding).WithMany(p => p.ProceedingAttendees)
                .HasForeignKey(d => d.RoleInProceedingId)
                .HasConstraintName("FK_ProceedingAttendees_RoleInProceeding");
        });

        modelBuilder.Entity<ProceedingDocument>(entity =>
        {
            entity.HasKey(e => e.DocumentId).HasName("PK__Proceedi__1ABEEF6F23CCBEE0");

            entity.ToTable("ProceedingDocuments", "leg");

            entity.Property(e => e.DocumentId).HasColumnName("DocumentID");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DocumentTitle)
                .IsRequired()
                .HasMaxLength(255);
            entity.Property(e => e.DocumentType)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FilePath).IsRequired();
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProceedingId).HasColumnName("ProceedingID");
            entity.Property(e => e.SubmittedBy).HasMaxLength(255);

            entity.HasOne(d => d.Proceeding).WithMany(p => p.ProceedingDocuments)
                .HasForeignKey(d => d.ProceedingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProceedingDocuments_Proceedings");
        });

        modelBuilder.Entity<ProceedingOutcome>(entity =>
        {
            entity.HasKey(e => e.OutcomeId).HasName("PK__Proceedi__113E6AFCF3B6CA06");

            entity.ToTable("ProceedingOutcomes", "leg");

            entity.Property(e => e.OutcomeId).HasColumnName("OutcomeID");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DocumentReference).HasMaxLength(255);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.OrderIssuedBy).HasMaxLength(255);
            entity.Property(e => e.OutcomeDescription).IsRequired();
            entity.Property(e => e.OutcomeTypeId).HasColumnName("OutcomeTypeID");
            entity.Property(e => e.ProceedingId).HasColumnName("ProceedingID");

            entity.HasOne(d => d.OutcomeType).WithMany(p => p.ProceedingOutcomes)
                .HasForeignKey(d => d.OutcomeTypeId)
                .HasConstraintName("FK_ProceedingOutcomes_OutcomeTypes");

            entity.HasOne(d => d.Proceeding).WithMany(p => p.ProceedingOutcomes)
                .HasForeignKey(d => d.ProceedingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProceedingOutcomes_Proceedings");
        });

        modelBuilder.Entity<ProceedingStatus>(entity =>
        {
            entity.HasKey(e => e.ProceedingStatusId).HasName("PK__Proceedi__0A5A96521D0AAD3C");

            entity.ToTable("ProceedingStatus", "leg");

            entity.HasIndex(e => e.StatusName, "UQ__Proceedi__05E7698A9B096BAD").IsUnique();

            entity.Property(e => e.ProceedingStatusId).HasColumnName("ProceedingStatusID");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.StatusName)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<ProceedingType>(entity =>
        {
            entity.HasKey(e => e.ProceedingTypeId).HasName("PK__Proceedi__A02E6C6B14BFD131");

            entity.ToTable("ProceedingTypes", "leg");

            entity.HasIndex(e => e.TypeName, "UQ__Proceedi__D4E7DFA82E32469A").IsUnique();

            entity.Property(e => e.ProceedingTypeId).HasColumnName("ProceedingTypeID");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.TypeName)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<RawEmployee>(entity =>
        {
            entity.HasKey(e => e.Cnic).HasName("PK__RawEmplo__AA570FD538F0E709");

            entity.Property(e => e.Cnic)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("CNIC");
            entity.Property(e => e.Department)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Designation)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.EmployeeCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.EmploymentStatus)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.FatherHusbandWifeName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("Father / Husband / Wife Name");
            entity.Property(e => e.Location)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.NatureOfContract)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Respondent>(entity =>
        {
            entity.ToTable("Respondents", "leg");

            entity.HasIndex(e => e.CourtCaseId, "IX_Respondents_CourtCaseId");

            entity.HasIndex(e => e.LawFirmId, "IX_Respondents_LawFirmId");

            entity.HasIndex(e => e.LawyerId, "IX_Respondents_LawyerId");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedBy)
                .IsRequired()
                .HasMaxLength(300);
            entity.Property(e => e.ModifiedBy)
                .IsRequired()
                .HasMaxLength(300);
            entity.Property(e => e.RespondentName)
                .IsRequired()
                .HasMaxLength(500);

            entity.HasOne(d => d.CourtCase).WithMany(p => p.Respondents)
                .HasForeignKey(d => d.CourtCaseId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.LawFirm).WithMany(p => p.Respondents).HasForeignKey(d => d.LawFirmId);

            entity.HasOne(d => d.Lawyer).WithMany(p => p.Respondents).HasForeignKey(d => d.LawyerId);
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("PK__Roles__8AFACE1ADD824D4D");

            entity.ToTable("Roles", "leg");

            entity.HasIndex(e => e.RoleName, "UQ__Roles__8A2B616076082C13").IsUnique();

            entity.Property(e => e.RoleId).ValueGeneratedNever();
            entity.Property(e => e.RoleName)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<RoleInProceeding>(entity =>
        {
            entity.HasKey(e => e.RoleInProceedingId).HasName("PK__RoleInPr__E6C82D53656A2C5B");

            entity.ToTable("RoleInProceeding", "leg");

            entity.HasIndex(e => e.RoleName, "UQ__RoleInPr__8A2B6160D067B034").IsUnique();

            entity.Property(e => e.RoleInProceedingId).HasColumnName("RoleInProceedingID");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.RoleName)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<StatyOrderType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__StatyOrd__3214EC07364BFD20");

            entity.ToTable("StatyOrderTypes", "leg");

            entity.HasIndex(e => e.Text, "UQ__StatyOrd__8F7960A3EBF3926A").IsUnique();

            entity.Property(e => e.Text)
                .IsRequired()
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<StayOrder>(entity =>
        {
            entity.ToTable("StayOrder");

            entity.Property(e => e.Text).IsRequired();
        });

        modelBuilder.Entity<TypeOfEmployment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TypeOfEm__3214EC0749BC6B2F");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("PK_Users_UserId");

            entity.ToTable("Users", "leg");

            entity.HasIndex(e => e.RoleId, "IX_Users_RoleId");

            entity.Property(e => e.UserId)
                .HasMaxLength(300)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Pcode)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("PCode");
            entity.Property(e => e.UserName)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.Role).WithMany(p => p.Users)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}