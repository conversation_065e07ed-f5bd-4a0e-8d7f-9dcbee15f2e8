using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CaseCategoryService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<CaseCategoryDto>> GetCategoriesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseCategories
            .Select(c => new CaseCategoryDto
            {
                Id = c.Id,
                Title = c.Title
            })
            .OrderBy(c => c.Title)
            .ToListAsync();
    }

    public async Task<CaseCategoryDto?> GetCategoryByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.CaseCategories
            .Where(c => c.Id == id)
            .Select(c => new CaseCategoryDto
            {
                Id = c.Id,
                Title = c.Title
            })
            .FirstOrDefaultAsync();
    }

    public async Task<string> SaveCategoryAsync(CaseCategoryDto dto, string userId)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();

            if (dto.Id == 0)
            {
                if (await dc.CaseCategories.AnyAsync(c => c.Title.ToLower() == dto.Title.ToLower()))
                    return "A category with this title already exists";

                var category = new CaseCategory
                {
                    Title = dto.Title.Trim(),
                    CreatedBy = userId,
                    CreateDate = DateTime.Now,
                    ModifiedBy = userId,
                    ModifiedDate = DateTime.Now
                };

                dc.CaseCategories.Add(category);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                var category = await dc.CaseCategories.FindAsync(dto.Id);
                if (category == null) return "Category not found";

                if (await dc.CaseCategories.AnyAsync(c =>
                        c.Id != dto.Id && c.Title.ToLower() == dto.Title.ToLower()))
                    return "A category with this title already exists";

                category.Title = dto.Title.Trim();
                category.ModifiedBy = userId;
                category.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeleteCategoryAsync(int id)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();

            var category = await dc.CaseCategories.FindAsync(id);
            if (category == null) return "Category not found";

            // Uncomment if you want to check for related cases before deletion
            //if (await dbContext.CourtCases.AnyAsync(c => c.CaseCategoryId == id))
            //{
            //    return "Cannot delete category as it is being used by one or more cases";
            //}

            dc.CaseCategories.Remove(category);
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }
}