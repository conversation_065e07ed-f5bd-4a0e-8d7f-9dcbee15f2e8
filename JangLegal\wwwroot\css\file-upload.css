/* File Upload Styles */
.file-upload-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-input {
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    min-height: 60px;
    text-align: center;
}

.file-upload-label:hover {
    border-color: #2196f3;
    background-color: #f3f9ff;
    color: #2196f3;
}

.file-upload-label:active {
    transform: translateY(1px);
}

.file-upload-label .material-icons {
    font-size: 24px;
}

/* File Preview Styles */
.file-preview {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    padding: 12px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-info .material-icons {
    color: #2196f3;
    font-size: 24px;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin-bottom: 2px;
}

.file-size {
    color: #666;
    font-size: 12px;
}

.btn-remove {
    background: none;
    border: none;
    color: #f44336;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.btn-remove:hover {
    background-color: #ffebee;
}

.btn-remove .material-icons {
    font-size: 18px;
}

/* File Requirements */
.file-requirements {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
}

.file-requirements .material-icons {
    font-size: 16px;
}

/* Error Messages */
.text-danger {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #f44336;
    font-size: 14px;
}

.text-danger .material-icons {
    font-size: 18px;
}

/* Success States */
.file-upload-success .file-upload-label {
    border-color: #4caf50;
    background-color: #f1f8e9;
    color: #4caf50;
}

/* Drag and Drop States */
.file-upload-label.drag-over {
    border-color: #2196f3;
    background-color: #e3f2fd;
    color: #2196f3;
    transform: scale(1.02);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .file-upload-label {
        padding: 16px 12px;
        font-size: 13px;
    }
    
    .file-upload-label .material-icons {
        font-size: 20px;
    }
    
    .file-info {
        gap: 8px;
    }
    
    .file-name {
        font-size: 13px;
    }
    
    .file-size {
        font-size: 11px;
    }
}

/* Animation for file selection */
@keyframes fileSelected {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-preview {
    animation: fileSelected 0.3s ease-out;
}

/* Loading state */
.file-upload-loading .file-upload-label {
    pointer-events: none;
    opacity: 0.6;
}

.file-upload-loading .file-upload-label::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #2196f3;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
