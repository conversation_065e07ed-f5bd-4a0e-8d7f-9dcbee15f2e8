using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class CaseProceedingDataService(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IWebHostEnvironment environment)
{
    #region Helper Methods

    private string GetDocumentUrl(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
            return "";

        // Convert physical path to URL path
        var webRootPath = environment.WebRootPath;
        if (filePath.StartsWith(webRootPath))
        {
            var relativePath = filePath.Substring(webRootPath.Length).Replace('\\', '/');
            return relativePath.StartsWith('/') ? relativePath : '/' + relativePath;
        }

        return "";
    }

    #endregion

    #region Proceeding CRUD Operations

    public async Task<List<CaseProceedingDto>> GetProceedingsByCaseIdAsync(int caseId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        var proceedings = await dc.CaseProceedings
            .Include(p => p.ProceedingType)
            .Include(p => p.ProceedingStatus)
            .Include(p => p.ProceedingOutcomes)
            .ThenInclude(o => o.OutcomeType)
            .Include(p => p.ProceedingAttendees)
            .ThenInclude(a => a.RoleInProceeding)
            .Include(p => p.ProceedingDocuments)
            .Where(p => p.CourtCaseId == caseId)
            .OrderBy(p => p.ProceedingDate)
            .Select(p => new CaseProceedingDto
            {
                ProceedingId = p.ProceedingId,
                CourtCaseId = p.CourtCaseId,
                ProceedingDate = p.ProceedingDate,
                PresidingJudge = p.PresidingJudge ?? "",
                CourtRoomNumber = p.CourtRoomNumber ?? "",
                NextProceedingDate = p.NextProceedingDate,

                Remarks = p.Remarks ?? "",
                CreatedDate = p.CreatedDate,
                ModifiedDate = p.ModifiedDate,
                ProceedingTypeId = p.ProceedingTypeId,
                ProceedingTypeName = p.ProceedingType != null ? p.ProceedingType.TypeName : "",
                ProceedingStatusId = p.ProceedingStatusId,
                ProceedingStatusName = p.ProceedingStatus != null ? p.ProceedingStatus.StatusName : "",
                ProceedingOutcomes = p.ProceedingOutcomes.Select(o => new ProceedingOutcomeDto
                {
                    OutcomeId = o.OutcomeId,
                    ProceedingId = o.ProceedingId,
                    OutcomeDescription = o.OutcomeDescription ?? "",
                    OrderIssuedBy = o.OrderIssuedBy ?? "",
                    OrderDate = o.OrderDate,
                    DocumentReference = o.DocumentReference ?? "",
                    //CreatedDate = o.CreatedDate,
                    //ModifiedDate = o.ModifiedDate,
                    OutcomeTypeId = o.OutcomeTypeId,
                    OutcomeTypeName = o.OutcomeType != null ? o.OutcomeType.TypeName : ""
                }).ToList(),
                ProceedingAttendees = p.ProceedingAttendees.Select(a => new ProceedingAttendeeDto
                {
                    AttendeeId = a.AttendeeId,
                    ProceedingId = a.ProceedingId,
                    PersonName = a.PersonName ?? "",
                    IsPresent = a.IsPresent,
                    CreatedDate = a.CreatedDate,
                    ModifiedDate = a.ModifiedDate,
                    RoleInProceedingId = a.RoleInProceedingId,
                    RoleInProceedingName = a.RoleInProceeding != null ? a.RoleInProceeding.RoleName : ""
                }).ToList(),
                ProceedingDocuments = p.ProceedingDocuments.Select(d => new ProceedingDocumentDto
                {
                    DocumentId = d.DocumentId,
                    ProceedingId = d.ProceedingId,
                    DocumentType = d.DocumentType ?? "",
                    DocumentTitle = d.DocumentTitle ?? "",
                    FilePath = d.FilePath ?? "",
                    SubmissionDate = d.SubmissionDate,
                    SubmittedBy = d.SubmittedBy ?? "",
                    Remarks = d.Remarks ?? "",
                    CreatedDate = d.CreatedDate,
                    ModifiedDate = d.ModifiedDate,
                    FileName = Path.GetFileName(d.FilePath ?? ""),
                    FileExtension = Path.GetExtension(d.FilePath ?? ""),
                    FileUrl = GetDocumentUrl(d.FilePath ?? "")
                }).ToList()
            })
            .ToListAsync();

        return proceedings;
    }

    public async Task<CaseProceedingDto?> GetProceedingByIdAsync(int proceedingId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        var proceeding = await dc.CaseProceedings
            .Include(p => p.ProceedingType)
            .Include(p => p.ProceedingStatus)
            .Include(p => p.ProceedingOutcomes)
            .ThenInclude(o => o.OutcomeType)
            .Include(p => p.ProceedingAttendees)
            .ThenInclude(a => a.RoleInProceeding)
            .Include(p => p.ProceedingDocuments)
            .Where(p => p.ProceedingId == proceedingId)
            .Select(p => new CaseProceedingDto
            {
                ProceedingId = p.ProceedingId,
                CourtCaseId = p.CourtCaseId,
                ProceedingDate = p.ProceedingDate,
                PresidingJudge = p.PresidingJudge ?? "",
                CourtRoomNumber = p.CourtRoomNumber ?? "",
                NextProceedingDate = p.NextProceedingDate,
                Remarks = p.Remarks ?? "",
                CreatedDate = p.CreatedDate,
                ModifiedDate = p.ModifiedDate,
                ProceedingTypeId = p.ProceedingTypeId,
                ProceedingTypeName = p.ProceedingType != null ? p.ProceedingType.TypeName : "",
                ProceedingStatusId = p.ProceedingStatusId,
                ProceedingStatusName = p.ProceedingStatus != null ? p.ProceedingStatus.StatusName : ""
            })
            .FirstOrDefaultAsync();

        return proceeding;
    }

    public async Task<(bool Success, string Message, int ProceedingId)> SaveProceedingAsync(CaseProceedingDto dto,
        string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            if (dto.ProceedingId == 0)
            {
                // Create new proceeding
                var proceeding = new CaseProceeding
                {
                    CourtCaseId = dto.CourtCaseId,
                    ProceedingDate = dto.ProceedingDate,
                    PresidingJudge = dto.PresidingJudge,
                    CourtRoomNumber = dto.CourtRoomNumber,
                    NextProceedingDate = dto.NextProceedingDate,
                    Remarks = dto.Remarks,
                    ProceedingTypeId = dto.ProceedingTypeId,
                    ProceedingStatusId = dto.ProceedingStatusId,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                dc.CaseProceedings.Add(proceeding);
                await dc.SaveChangesAsync();

                return (true, "Proceeding saved successfully", proceeding.ProceedingId);
            }
            else
            {
                // Update existing proceeding
                var proceeding = await dc.CaseProceedings.FindAsync(dto.ProceedingId);
                if (proceeding == null)
                    return (false, "Proceeding not found", 0);

                proceeding.ProceedingDate = dto.ProceedingDate;
                proceeding.PresidingJudge = dto.PresidingJudge;
                proceeding.CourtRoomNumber = dto.CourtRoomNumber;
                proceeding.NextProceedingDate = dto.NextProceedingDate;
                proceeding.Remarks = dto.Remarks;
                proceeding.ProceedingTypeId = dto.ProceedingTypeId;
                proceeding.ProceedingStatusId = dto.ProceedingStatusId;
                proceeding.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();

                return (true, "Proceeding updated successfully", proceeding.ProceedingId);
            }
        }
        catch (Exception ex)
        {
            return (false, $"Error saving proceeding: {ex.Message}", 0);
        }
    }

    public async Task<(bool Success, string Message)> DeleteProceedingAsync(int proceedingId, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            var proceeding = await dc.CaseProceedings
                .Include(p => p.ProceedingOutcomes)
                .Include(p => p.ProceedingAttendees)
                .Include(p => p.ProceedingDocuments)
                .FirstOrDefaultAsync(p => p.ProceedingId == proceedingId);

            if (proceeding == null)
                return (false, "Proceeding not found");

            // Delete related documents from file system
            foreach (var doc in proceeding.ProceedingDocuments)
                if (!string.IsNullOrEmpty(doc.FilePath) && File.Exists(doc.FilePath))
                    File.Delete(doc.FilePath);

            // Remove all related entities
            dc.ProceedingOutcomes.RemoveRange(proceeding.ProceedingOutcomes);
            dc.ProceedingAttendees.RemoveRange(proceeding.ProceedingAttendees);
            dc.ProceedingDocuments.RemoveRange(proceeding.ProceedingDocuments);
            dc.CaseProceedings.Remove(proceeding);

            await dc.SaveChangesAsync();

            return (true, "Proceeding deleted successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error deleting proceeding: {ex.Message}");
        }
    }

    #endregion

    #region Lookup Data Methods

    public async Task<List<ProceedingTypeDto>> GetProceedingTypesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        return await dc.ProceedingTypes
            .Select(pt => new ProceedingTypeDto
            {
                ProceedingTypeId = pt.ProceedingTypeId,
                TypeName = pt.TypeName ?? "",
                Description = pt.Description ?? ""
            })
            .OrderBy(pt => pt.TypeName)
            .ToListAsync();
    }

    public async Task<List<ProceedingStatusDto>> GetProceedingStatusesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        return await dc.ProceedingStatuses
            .Select(ps => new ProceedingStatusDto
            {
                ProceedingStatusId = ps.ProceedingStatusId,
                StatusName = ps.StatusName ?? "",
                Description = ps.Description ?? ""
            })
            .OrderBy(ps => ps.StatusName)
            .ToListAsync();
    }

    public async Task<List<RoleInProceedingDto>> GetRoleInProceedingsAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        return await dc.RoleInProceedings
            .Select(rp => new RoleInProceedingDto
            {
                RoleInProceedingId = rp.RoleInProceedingId,
                RoleName = rp.RoleName ?? "",
                Description = rp.Description ?? ""
            })
            .OrderBy(rp => rp.RoleName)
            .ToListAsync();
    }

    public async Task<List<OutcomeTypeDto>> GetOutcomeTypesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        return await dc.OutcomeTypes
            .Select(ot => new OutcomeTypeDto
            {
                OutcomeTypeId = ot.OutcomeTypeId,
                TypeName = ot.TypeName ?? "",
                Description = ot.Description ?? ""
            })
            .OrderBy(ot => ot.TypeName)
            .ToListAsync();
    }

    #endregion

    #region Outcome Management

    public async Task<(bool Success, string Message)> SaveOutcomeAsync(ProceedingOutcomeDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            if (dto.OutcomeId == 0)
            {
                // Create new outcome
                var outcome = new ProceedingOutcome
                {
                    ProceedingId = dto.ProceedingId,
                    OutcomeDescription = dto.OutcomeDescription,
                    OrderIssuedBy = dto.OrderIssuedBy,
                    OrderDate = dto.OrderDate,
                    DocumentReference = dto.DocumentReference,
                    OutcomeTypeId = dto.OutcomeTypeId,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                dc.ProceedingOutcomes.Add(outcome);
                await dc.SaveChangesAsync();

                return (true, "Outcome saved successfully");
            }
            else
            {
                // Update existing outcome
                var outcome = await dc.ProceedingOutcomes.FindAsync(dto.OutcomeId);
                if (outcome == null)
                    return (false, "Outcome not found");

                outcome.OutcomeDescription = dto.OutcomeDescription;
                outcome.OrderIssuedBy = dto.OrderIssuedBy;
                outcome.OrderDate = dto.OrderDate;
                outcome.DocumentReference = dto.DocumentReference;
                outcome.OutcomeTypeId = dto.OutcomeTypeId;
                outcome.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();

                return (true, "Outcome updated successfully");
            }
        }
        catch (Exception ex)
        {
            return (false, $"Error saving outcome: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> DeleteOutcomeAsync(int outcomeId, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            var outcome = await dc.ProceedingOutcomes.FindAsync(outcomeId);
            if (outcome == null)
                return (false, "Outcome not found");

            dc.ProceedingOutcomes.Remove(outcome);
            await dc.SaveChangesAsync();

            return (true, "Outcome deleted successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error deleting outcome: {ex.Message}");
        }
    }

    #endregion

    #region Attendee Management

    public async Task<(bool Success, string Message)> SaveAttendeeAsync(ProceedingAttendeeDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            if (dto.AttendeeId == 0)
            {
                // Create new attendee
                var attendee = new ProceedingAttendee
                {
                    ProceedingId = dto.ProceedingId,
                    PersonName = dto.PersonName,
                    IsPresent = dto.IsPresent,
                    RoleInProceedingId = dto.RoleInProceedingId,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                dc.ProceedingAttendees.Add(attendee);
                await dc.SaveChangesAsync();

                return (true, "Attendee saved successfully");
            }
            else
            {
                // Update existing attendee
                var attendee = await dc.ProceedingAttendees.FindAsync(dto.AttendeeId);
                if (attendee == null)
                    return (false, "Attendee not found");

                attendee.PersonName = dto.PersonName;
                attendee.IsPresent = dto.IsPresent;
                attendee.RoleInProceedingId = dto.RoleInProceedingId;
                attendee.ModifiedDate = DateTime.Now;

                await dc.SaveChangesAsync();

                return (true, "Attendee updated successfully");
            }
        }
        catch (Exception ex)
        {
            return (false, $"Error saving attendee: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> DeleteAttendeeAsync(int attendeeId, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            var attendee = await dc.ProceedingAttendees.FindAsync(attendeeId);
            if (attendee == null)
                return (false, "Attendee not found");

            dc.ProceedingAttendees.Remove(attendee);
            await dc.SaveChangesAsync();

            return (true, "Attendee deleted successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error deleting attendee: {ex.Message}");
        }
    }

    #endregion

    #region Document Management

    public async Task<(bool Success, string Message, ProceedingDocumentDto? Document)> SaveDocumentAsync(
        IBrowserFile file,
        ProceedingDocumentDto dto,
        int caseId,
        int proceedingId,
        string userId)
    {
        try
        {
            // Validate file
            var validationResult = ValidateFile(file);
            if (!validationResult.IsValid) return (false, validationResult.ErrorMessage, null);

            // Create directory structure: /proceedings/{caseId}/{proceedingId}/
            var uploadsPath = Path.Combine(environment.WebRootPath, "proceedings", caseId.ToString(),
                proceedingId.ToString());
            Directory.CreateDirectory(uploadsPath);

            // Generate unique filename with GUID: guid-file-name.ext
            var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(file.Name);
            var uniqueFileName = $"{Guid.NewGuid()}-{fileNameWithoutExtension}{fileExtension}";
            var filePath = Path.Combine(uploadsPath, uniqueFileName);

            // Save file to disk
            await using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.OpenReadStream(10 * 1024 * 1024).CopyToAsync(stream); // 10MB limit
            }

            // Save to database
            await using var dc = await dbContextFactory.CreateDbContextAsync();
            var document = new ProceedingDocument
            {
                ProceedingId = proceedingId,
                DocumentType = dto.DocumentType,
                DocumentTitle = dto.DocumentTitle,
                FilePath = filePath,
                SubmissionDate = dto.SubmissionDate,
                SubmittedBy = dto.SubmittedBy,
                Remarks = dto.Remarks,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };

            dc.ProceedingDocuments.Add(document);
            await dc.SaveChangesAsync();

            var documentDto = new ProceedingDocumentDto
            {
                DocumentId = document.DocumentId,
                ProceedingId = document.ProceedingId,
                DocumentType = document.DocumentType,
                DocumentTitle = document.DocumentTitle,
                FilePath = document.FilePath,
                SubmissionDate = document.SubmissionDate,
                SubmittedBy = document.SubmittedBy,
                Remarks = document.Remarks,
                CreatedDate = document.CreatedDate,
                ModifiedDate = document.ModifiedDate,
                FileName = file.Name,
                FileExtension = fileExtension,
                FileSizeBytes = file.Size,
                FileUrl = GetDocumentUrl(filePath)
            };

            return (true, "Document uploaded successfully", documentDto);
        }
        catch (Exception ex)
        {
            return (false, $"Error uploading document: {ex.Message}", null);
        }
    }

    public async Task<(bool Success, string Message)> DeleteDocumentAsync(int documentId, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            var document = await dc.ProceedingDocuments.FindAsync(documentId);
            if (document == null)
                return (false, "Document not found");

            // Delete file from disk
            if (!string.IsNullOrEmpty(document.FilePath) && File.Exists(document.FilePath))
                File.Delete(document.FilePath);

            dc.ProceedingDocuments.Remove(document);
            await dc.SaveChangesAsync();

            return (true, "Document deleted successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error deleting document: {ex.Message}");
        }
    }

    public async Task<(bool Success, byte[]? FileData, string? FileName, string? ContentType)> DownloadDocumentAsync(
        int documentId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();

        try
        {
            var document = await dc.ProceedingDocuments.FindAsync(documentId);
            if (document == null)
                return (false, null, null, null);

            if (string.IsNullOrEmpty(document.FilePath) || !File.Exists(document.FilePath))
                return (false, null, null, null);

            var fileData = await File.ReadAllBytesAsync(document.FilePath);
            var fileName = document.DocumentTitle + Path.GetExtension(document.FilePath);
            var contentType = GetContentType(Path.GetExtension(document.FilePath));

            return (true, fileData, fileName, contentType);
        }
        catch (Exception )
        {
            return (false, null, null, null);
        }
    }

    private (bool IsValid, string ErrorMessage) ValidateFile(IBrowserFile file)
    {
        // Check file size (10MB limit)
        if (file.Size > 10 * 1024 * 1024) return (false, "File size cannot exceed 10MB");

        // Check file extension
        var allowedExtensions = new[]
            { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
        var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
            return (false, "File type not allowed. Allowed types: PDF, Word, Excel, Images");

        return (true, "");
    }

    private string GetContentType(string fileExtension)
    {
        return fileExtension.ToLowerInvariant() switch
        {
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            _ => "application/octet-stream"
        };
    }

    #endregion
}