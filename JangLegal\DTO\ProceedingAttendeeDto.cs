using System.ComponentModel.DataAnnotations;

namespace JangLegal.DTO;

public class ProceedingAttendeeDto
{
    public int AttendeeId { get; set; }
    
    [Required(ErrorMessage = "Proceeding ID is required")]
    public int ProceedingId { get; set; }
    
    [Required(ErrorMessage = "Person name is required")]
    [StringLength(255, ErrorMessage = "Person name cannot be longer than 255 characters")]
    public string PersonName { get; set; } = string.Empty;
    
    public bool IsPresent { get; set; } = true;
    
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    
    public int? RoleInProceedingId { get; set; }
    public string RoleInProceedingName { get; set; } = string.Empty;
    
    // Computed properties for display
    public string IsPresentText => IsPresent ? "Present" : "Absent";
}
