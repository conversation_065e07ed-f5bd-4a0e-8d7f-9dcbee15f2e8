﻿{
   "CodeGenerationMode": 5,
   "ContextClassName": "ApplicationDbContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "JangLegal",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[BusinessUnits]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CaseCategories]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CaseEmployees]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CaseEmployeeStatuses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CaseVerdicts]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Cities]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Companies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CouncilOfGroupCompanies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CouncilOfNonGroupCompanies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Departments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Designations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Employees]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[EmploymentStatus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Judges]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[MaritalStatus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[NatureOfContracts]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PlaintiffTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RawEmployees]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[StayOrder]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TypeOfEmployments]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Attachments]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[AttachmentTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[CaseNatures]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[CaseProceedings]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[CourtCases]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Courts]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[GroupCompanies]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[LawFirms]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Lawyers]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[NonGroupCompanies]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[OutcomeTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Plaintiffs]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[ProceedingAttendees]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[ProceedingDocuments]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[ProceedingOutcomes]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[ProceedingStatus]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[ProceedingTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Respondents]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[RoleInProceeding]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Roles]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[StatyOrderTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[leg].[Users]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}