using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services
{
    public class CourtDataService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
    {
        public Task<List<CourtDto>> GetCourtsAsync()
        {
            using var dc = dbContextFactory.CreateDbContext();
            var courts = dc.Courts
                .Select(c => new CourtDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    City = c.City
                })
                .OrderBy(c => c.Name)
                .ThenBy(c => c.City)
                .ToList();

            return Task.FromResult(courts);
        }

        public Task<CourtDto?> GetCourtByIdAsync(int id)
        {
            using var dc = dbContextFactory.CreateDbContext();
            var court = dc.Courts
                .Where(c => c.Id == id)
                .Select(c => new CourtDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    City = c.City
                })
                .FirstOrDefault();

            return Task.FromResult(court);
        }

        public Task<List<CourtDto>> GetCourtsByCityAsync(string city)
        {
            using var dc = dbContextFactory.CreateDbContext();
            var courts = dc.Courts
                .Where(c => c.City.ToLower() == city.ToLower())
                .Select(c => new CourtDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    City = c.City
                })
                .OrderBy(c => c.Name)
                .ToList();

            return Task.FromResult(courts);
        }

        public Task<string> SaveCourtAsync(CourtDto dto, string userId)
        {
            using var dc = dbContextFactory.CreateDbContext();
            try
            {
                if (dto.Id == 0)
                {
                    // Check if court with same name and city already exists
                    if (dc.Courts.Any(c => c.Name == dto.Name && c.City == dto.City))
                    {
                        return Task.FromResult("A court with this name already exists in this city");
                    }

                    // Create new court
                    var court = new Court
                    {
                        Name = dto.Name,
                        City = dto.City,
                        CreateBy = userId,
                        CreateDate = DateTime.Now,
                        ModifiedBy = userId,
                        ModifiedDate = DateTime.Now
                    };

                    dc.Courts.Add(court);
                    dc.SaveChanges();
                    return Task.FromResult("OK");
                }
                else
                {
                    // Update existing court
                    var court = dc.Courts.Find(dto.Id);
                    if (court == null)
                    {
                        return Task.FromResult("Court not found");
                    }

                    // Check if another court with same name and city already exists
                    if (dc.Courts.Any(c => c.Name == dto.Name && c.City == dto.City && c.Id != dto.Id))
                    {
                        return Task.FromResult("Another court with this name already exists in this city");
                    }

                    court.Name = dto.Name;
                    court.City = dto.City;
                    court.ModifiedBy = userId;
                    court.ModifiedDate = DateTime.Now;

                    dc.SaveChanges();
                    return Task.FromResult("OK");
                }
            }
            catch (Exception ex)
            {
                return Task.FromResult(ex.Message);
            }
        }

        public Task<string> DeleteCourtAsync(int id)
        {
            using var dc = dbContextFactory.CreateDbContext();
            try
            {
                var court = dc.Courts.Find(id);
                if (court == null)
                {
                    return Task.FromResult("Court not found");
                }

                // Check if court is referenced in any cases
                //var isCourtReferenced = dc.CourtCases.Any(c => c.CourtId == id);
                //if (isCourtReferenced)
                //{
                //    return Task.FromResult("Cannot delete court as it is referenced in one or more cases");
                //}

                dc.Courts.Remove(court);
                dc.SaveChanges();
                return Task.FromResult("OK");
            }
            catch (Exception ex)
            {
                return Task.FromResult(ex.Message);
            }
        }
    }
}