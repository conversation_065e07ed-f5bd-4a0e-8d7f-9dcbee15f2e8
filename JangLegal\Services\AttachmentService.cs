using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class AttachmentService(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IWebHostEnvironment environment,
    ILogger<AttachmentService> logger)
{
    // Allowed file extensions
    private static readonly string[] AllowedExtensions =
    [
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", 
        ".jpg", ".jpeg", ".png", ".gif", ".bmp"
    ];

    // Maximum file size (10MB)
    private const long MaxFileSize = 10 * 1024 * 1024;

    public async Task<List<AttachmentTypeDto>> GetAttachmentTypesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.AttachmentTypes
            .Select(at => new AttachmentTypeDto
            {
                Id = at.Id,
                Title = at.Title
            })
            .OrderBy(at => at.Title)
            .ToListAsync();
    }

    public async Task<List<AttachmentDto>> GetAttachmentsByCourtCaseIdAsync(int courtCaseId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.Attachments
            .Where(a => a.CourtCaseId == courtCaseId)
            .Include(a => a.AttachmentType)
            .Select(a => new AttachmentDto
            {
                Id = a.Id,
                CourtCaseId = a.CourtCaseId,
                FileName = a.FileName,
                
                FilePhysicalPath = a.FilePhysicalPath,
                FileUrl = a.FileUrl,
                CreatedDate = a.CreatedDate,
                CreatedBy = a.CreatedBy,
                ModifiedBy = a.ModifiedBy,
                ModifiedDate = a.ModifiedDate,
                AttachmentTypeId = a.AttachmentTypeId,
                AttachmentTypeName = a.AttachmentType != null ? a.AttachmentType.Title : ""
            })
            .OrderByDescending(a => a.CreatedDate)
            .ToListAsync();
    }

    public async Task<(bool Success, string Message, AttachmentDto? Attachment)> SaveAttachmentAsync(
        IBrowserFile file,
        string fileName,
        int courtCaseId,
        int attachmentTypeId,
        string userId)
    {
        try
        {
            // Validate file
            var validationResult = ValidateFile(file);
            if (!validationResult.IsValid)
            {
                return (false, validationResult.ErrorMessage, null);
            }

            // Create uploads directory if it doesn't exist
            var uploadsPath = Path.Combine(environment.WebRootPath, "uploads", "attachments");
            Directory.CreateDirectory(uploadsPath);

            // Generate unique filename
            var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();
            var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
            var filePath = Path.Combine(uploadsPath, uniqueFileName);

            // Save file to disk
            await using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.OpenReadStream(MaxFileSize).CopyToAsync(stream);
            }

            // Save to database
            await using var dc = await dbContextFactory.CreateDbContextAsync();
            var attachment = new Attachment
            {
                Id = Guid.NewGuid(),
                CourtCaseId = courtCaseId,
                FileName = !string.IsNullOrWhiteSpace(fileName) ? fileName : file.Name,
                FileType = fileExtension,
                FilePhysicalPath = filePath,
                FileUrl = $"/uploads/attachments/{uniqueFileName}",
                AttachmentTypeId = attachmentTypeId,
                CreatedBy = userId,
                CreatedDate = DateTime.Now,
                ModifiedBy = userId,
                ModifiedDate = DateTime.Now
            };

            dc.Attachments.Add(attachment);
            await dc.SaveChangesAsync();

            // Get attachment type name for return
            var attachmentType = await dc.AttachmentTypes.FindAsync(attachmentTypeId);

            var attachmentDto = new AttachmentDto
            {
                Id = attachment.Id,
                CourtCaseId = attachment.CourtCaseId,
                FileName = attachment.FileName,
                
                FilePhysicalPath = attachment.FilePhysicalPath,
                FileUrl = attachment.FileUrl,
                CreatedDate = attachment.CreatedDate,
                CreatedBy = attachment.CreatedBy,
                ModifiedBy = attachment.ModifiedBy,
                ModifiedDate = attachment.ModifiedDate,
                AttachmentTypeId = attachment.AttachmentTypeId,
                AttachmentTypeName = attachmentType?.Title ?? "",
                FileSizeBytes = file.Size
            };

            return (true, "File uploaded successfully", attachmentDto);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving attachment for court case {CourtCaseId}", courtCaseId);
            return (false, "An error occurred while saving the file", null);
        }
    }

    public async Task<(bool Success, string Message)> DeleteAttachmentAsync(Guid attachmentId, string userId)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();
            var attachment = await dc.Attachments.FindAsync(attachmentId);
            
            if (attachment == null)
            {
                return (false, "Attachment not found");
            }

            // Delete physical file
            if (File.Exists(attachment.FilePhysicalPath))
            {
                File.Delete(attachment.FilePhysicalPath);
            }

            // Delete from database
            dc.Attachments.Remove(attachment);
            await dc.SaveChangesAsync();

            return (true, "Attachment deleted successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deleting attachment {AttachmentId}", attachmentId);
            return (false, "An error occurred while deleting the file");
        }
    }

    private static (bool IsValid, string ErrorMessage) ValidateFile(IBrowserFile file)
    {
        if (file == null || file.Size == 0)
        {
            return (false, "Please select a file");
        }

        if (file.Size > MaxFileSize)
        {
            return (false, $"File size cannot exceed {MaxFileSize / (1024 * 1024)} MB");
        }

        var extension = Path.GetExtension(file.Name).ToLowerInvariant();
        if (!AllowedExtensions.Contains(extension))
        {
            var allowedTypes = string.Join(", ", AllowedExtensions);
            return (false, $"File type not allowed. Allowed types: {allowedTypes}");
        }

        return (true, string.Empty);
    }
}
