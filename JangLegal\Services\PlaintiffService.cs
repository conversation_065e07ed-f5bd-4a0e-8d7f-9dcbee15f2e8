using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class PlaintiffService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<PlaintiffDto>> GetPlaintiffsAsync(int courtCaseId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.Plaintiffs
            .Where(p => p.CourtCaseId == courtCaseId)
            .Select(p => new PlaintiffDto
            {
                Id = p.Id,
                CourtCaseId = p.CourtCaseId,
                CaseEmployeeId = p.CaseEmployeeId,
                EmployeeName = p.CaseEmployee.Name,
                EmployeeCode = p.CaseEmployee.EmployeeCode,
                CouncilOfGroupCompanyId = p.CouncilOfGroupCompanyId,
                CouncilOfGroupCompanyName = p.CouncilOfGroupCompany.Title,
                CouncilOfNonGroupCompanyId = p.CouncilOfNonGroupCompanyId,
                CouncilOfNonGroupCompanyName = p.CouncilOfNonGroupCompany.Title,
                CreatedBy = p.CreatedBy,
                CreatedDate = p.CreatedDate,
                ModifiedBy = p.ModifiedBy,
                ModifiedDate = p.ModifiedDate
            })
            .ToListAsync();
    }

    public async Task<PlaintiffDto> GetPlaintiffByIdAsync(Guid id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        var plaintiff = await dc.Plaintiffs
            .Include(p => p.CaseEmployee)
            .Include(p => p.CouncilOfGroupCompany)
            .Include(p => p.CouncilOfNonGroupCompany)
            .FirstAsync(p => p.Id == id);

        return MapToDto(plaintiff);
    }

    public async Task<string> SavePlaintiffAsync(PlaintiffDto dto, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            var plaintiff = dto.Id == Guid.Empty ? 
                new Plaintiff { Id = Guid.NewGuid() } : 
                await dc.Plaintiffs.FindAsync(dto.Id);

            if (plaintiff == null) return "Plaintiff not found";

            plaintiff.CourtCaseId = dto.CourtCaseId;
            plaintiff.CaseEmployeeId = dto.CaseEmployeeId;
            plaintiff.CouncilOfGroupCompanyId = dto.CouncilOfGroupCompanyId;
            plaintiff.CouncilOfNonGroupCompanyId = dto.CouncilOfNonGroupCompanyId;
            plaintiff.ModifiedBy = userId;
            plaintiff.ModifiedDate = DateTime.Now;

            if (dto.Id == Guid.Empty)
            {
                plaintiff.CreatedBy = userId;
                plaintiff.CreatedDate = DateTime.Now;
                dc.Plaintiffs.Add(plaintiff);
            }

            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public async Task<string> DeletePlaintiffAsync(Guid id, string userId)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        try
        {
            var plaintiff = await dc.Plaintiffs.FindAsync(id);
            if (plaintiff == null) return "Plaintiff not found";

            
            plaintiff.ModifiedBy = userId;
            plaintiff.ModifiedDate = DateTime.Now;

            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    private PlaintiffDto MapToDto(Plaintiff plaintiff)
    {
        return new PlaintiffDto
        {
            Id = plaintiff.Id,
            CourtCaseId = plaintiff.CourtCaseId,
            CaseEmployeeId = plaintiff.CaseEmployee.Id,
            EmployeeName = plaintiff.CaseEmployee?.Name,
            EmployeeCode = plaintiff.CaseEmployee?.EmployeeCode,
            CouncilOfGroupCompanyId = plaintiff.CouncilOfGroupCompanyId,
            CouncilOfGroupCompanyName = plaintiff.CouncilOfGroupCompany?.Title,
            CouncilOfNonGroupCompanyId = plaintiff.CouncilOfNonGroupCompanyId,
            CouncilOfNonGroupCompanyName = plaintiff.CouncilOfNonGroupCompany?.Title,
            CreatedBy = plaintiff.CreatedBy,
            CreatedDate = plaintiff.CreatedDate,
            ModifiedBy = plaintiff.ModifiedBy,
            ModifiedDate = plaintiff.ModifiedDate
        };
    }
}

